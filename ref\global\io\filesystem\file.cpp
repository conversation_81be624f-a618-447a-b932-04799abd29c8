﻿#include "file.h"
#include <filesystem>

namespace fs = std::filesystem;
namespace filesystem {

File::File() : m_file(nullptr) {
    setOpenMode(OpenMode::NotOpen);
}

File::File(const std::string& fileName) : m_fileName(fileName), m_file(nullptr) {
    setOpenMode(OpenMode::NotOpen);
}

File::File(const Path& fileName) : m_fileName(fileName.toString()), m_file(nullptr) {
    setOpenMode(OpenMode::NotOpen);
}

File::~File() {
    close();
}

bool File::open(OpenMode mode) {
    if (m_fileName.empty()) {
        m_errorString = "No file name specified";
        return false;
    }

    // Close if already open
    if (isOpen()) {
        close();
    }

    // Convert mode to std::ios_base::openmode
    std::ios_base::openmode iosMode = convertOpenMode(mode);

    // Create the file stream
    m_file = std::make_unique<std::fstream>();
    m_file->open(m_fileName, iosMode);

    if (!m_file->is_open()) {
        m_errorString = "Failed to open file: " + m_fileName;
        m_file.reset();
        return false;
    }

    setOpenMode(mode);
    return true;
}

void File::close() {
    if (m_file) {
        m_file->close();
        m_file.reset();
    }
    setOpenMode(OpenMode::NotOpen);
}

bool File::isOpen() const {
    return m_file && m_file->is_open();
}

IODevice::OpenMode File::openMode() const {
    return IODevice::openMode();
}

int64_t File::size() const {
    if (!isOpen()) {
        return -1;
    }

    try {
        return fs::file_size(m_fileName);
    } catch (const fs::filesystem_error& e) {
        m_errorString = e.what();
        return -1;
    }
}

int64_t File::pos() const {
    if (!isOpen()) {
        return -1;
    }

    return m_file->tellg();
}

int64_t File::seek(int64_t pos, SeekOrigin origin) {
    if (!isOpen()) {
        m_errorString = "File not open";
        return -1;
    }

    std::ios_base::seekdir dir;
    switch (origin) {
    case SeekOrigin::Begin:
        dir = std::ios_base::beg;
        break;
    case SeekOrigin::Current:
        dir = std::ios_base::cur;
        break;
    case SeekOrigin::End:
        dir = std::ios_base::end;
        break;
    default:
        m_errorString = "Invalid seek origin";
        return -1;
    }

    // Clear any error flags
    m_file->clear();

    // Seek to the position
    m_file->seekg(pos, dir);
    m_file->seekp(pos, dir);

    if (m_file->fail()) {
        m_errorString = "Seek failed";
        return -1;
    }

    return m_file->tellg();
}

bool File::atEnd() const {
    if (!isOpen()) {
        return true;
    }

    return m_file->eof();
}

int64_t File::read(char* data, int64_t maxSize) {
    if (!isOpen()) {
        m_errorString = "File not open";
        return -1;
    }

    // Use static_cast to perform binary operation with enum class
    if ((static_cast<int>(openMode()) & static_cast<int>(OpenMode::ReadOnly)) == 0) {
        m_errorString = "File not open for reading";
        return -1;
    }

    // Clear any error flags
    m_file->clear();

    // Read the data
    m_file->read(data, maxSize);

    // Check for errors
    if (m_file->bad()) {
        m_errorString = "Read error";
        return -1;
    }

    // Return the number of bytes read
    return m_file->gcount();
}

int64_t File::write(const char* data, int64_t size) {
    if (!isOpen()) {
        m_errorString = "File not open";
        return -1;
    }

    // Use static_cast to perform binary operation with enum class
    if ((static_cast<int>(openMode()) & static_cast<int>(OpenMode::WriteOnly)) == 0) {
        m_errorString = "File not open for writing";
        return -1;
    }

    // Clear any error flags
    m_file->clear();

    // Write the data
    m_file->write(data, size);

    // Check for errors
    if (m_file->bad()) {
        m_errorString = "Write error";
        return -1;
    }

    return size;
}

bool File::flush() {
    if (!isOpen()) {
        m_errorString = "File not open";
        return false;
    }

    m_file->flush();
    return !m_file->bad();
}

std::string File::errorString() const {
    return m_errorString;
}

std::string File::fileName() const {
    return m_fileName;
}

void File::setFileName(const std::string& fileName) {
    if (isOpen()) {
        close();
    }
    m_fileName = fileName;
}

void File::setFileName(const Path& fileName) {
    setFileName(fileName.toString());
}

bool File::exists() const {
    return fs::exists(m_fileName);
}

bool File::remove() {
    if (isOpen()) {
        close();
    }

    try {
        return fs::remove(m_fileName);
    } catch (const fs::filesystem_error& e) {
        m_errorString = e.what();
        return false;
    }
}

bool File::rename(const std::string& newName) {
    if (isOpen()) {
        close();
    }

    try {
        fs::rename(m_fileName, newName);
        m_fileName = newName;
        return true;
    } catch (const fs::filesystem_error& e) {
        m_errorString = e.what();
        return false;
    }
}

bool File::copy(const std::string& newName) {
    if (isOpen()) {
        close();
    }

    try {
        fs::copy_file(m_fileName, newName, fs::copy_options::overwrite_existing);
        return true;
    } catch (const fs::filesystem_error& e) {
        m_errorString = e.what();
        return false;
    }
}

std::filesystem::perms File::permissions() const {
    try {
        return fs::status(m_fileName).permissions();
    } catch (const fs::filesystem_error& e) {
        m_errorString = e.what();
        return fs::perms::none;
    }
}

bool File::setPermissions(std::filesystem::perms perms) {
    try {
        fs::permissions(m_fileName, perms);
        return true;
    } catch (const fs::filesystem_error& e) {
        m_errorString = e.what();
        return false;
    }
}

std::ios_base::openmode File::convertOpenMode(OpenMode mode) const {
    std::ios_base::openmode result = std::ios_base::openmode(0);

    // Use static_cast to perform binary operations with enum class
    if ((static_cast<int>(mode) & static_cast<int>(OpenMode::ReadOnly)) != 0) {
        result |= std::ios_base::in;
    }

    if ((static_cast<int>(mode) & static_cast<int>(OpenMode::WriteOnly)) != 0) {
        result |= std::ios_base::out;
    }

    if ((static_cast<int>(mode) & static_cast<int>(OpenMode::Append)) != 0) {
        result |= std::ios_base::app;
    }

    if ((static_cast<int>(mode) & static_cast<int>(OpenMode::Truncate)) != 0) {
        result |= std::ios_base::trunc;
    }

    if ((static_cast<int>(mode) & static_cast<int>(OpenMode::Text)) != 0) {
        // No specific flag for text mode in C++
    } else if ((static_cast<int>(mode) & static_cast<int>(OpenMode::Binary)) != 0) {
        result |= std::ios_base::binary;
    }

    return result;
}

} // namespace filesystem
