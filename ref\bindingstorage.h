#ifndef BINDINGSTORAGE_H
#define BINDINGSTORAGE_H

typedef void * HANDLE;  // #include <QtCore/qnamespace.h>

template <typename Class, typename T, auto Offset, auto Setter, auto Signal, auto Getter>
class ObjectCompatProperty;
struct PropertyDelayedNotifications;
class UntypedPropertyData;

class PropertyBindingData;
struct BindingEvaluationState;
struct CompatPropertySafePoint;

struct BindingStatus
{
    BindingEvaluationState *currentlyEvaluatingBinding = nullptr;
    CompatPropertySafePoint *currentCompatProperty = nullptr;
    HANDLE threadId = nullptr;
    PropertyDelayedNotifications *groupUpdateData = nullptr;
};

struct BindingStatusAccessToken;
BindingStatus *getBindingStatus(BindingStatusAccessToken);

struct BindingStorageData;
class BindingStorage
{
    mutable BindingStorageData *d = nullptr;
    BindingStatus *bindingStatus = nullptr;

    template<typename Class, typename T, auto Offset, auto Setter, auto Signal, auto Getter>
    friend class ObjectCompatProperty;
    friend class ObjectPrivate;
    friend class PropertyBindingData;
public:
    BindingStorage();
    ~BindingStorage();

    bool isEmpty() { return !d; }
    bool isValid() const noexcept { return bindingStatus; }

    const BindingStatus *status(BindingStatusAccessToken) const;

    void registerDependency(const UntypedPropertyData *data) const
    {
        if (!bindingStatus || !bindingStatus->currentlyEvaluatingBinding)
            return;
        registerDependency_helper(data);
    }
    PropertyBindingData *bindingData(const UntypedPropertyData *data) const
    {
        if (!d)
            return nullptr;
        return bindingData_helper(data);
    }

    PropertyBindingData *bindingData(UntypedPropertyData *data, bool create)
    {
        if (!d && !create)
            return nullptr;
        return bindingData_helper(data, create);
    }
private:
    void reinitAfterThreadMove();
    void clear();
    void registerDependency_helper(const UntypedPropertyData *data) const;

    PropertyBindingData *bindingData_helper(const UntypedPropertyData *data) const;
    PropertyBindingData *bindingData_helper(UntypedPropertyData *data, bool create);
};

#endif // BINDINGSTORAGE_H
