﻿#ifndef FILEINFO_H
#define FILEINFO_H

#include <string>
#include <chrono>
#include <cstdint>
#include <filesystem>

#ifdef _WIN32
#include <windows.h>
#endif

#include "path.h"

namespace fs = std::filesystem;

namespace filesystem {

/**
 * @brief The FileInfo class provides information about a file.
 * 
 * FileInfo provides information about a file, such as its name,
 * path, size, and timestamps.
 */
class FileInfo {
public:
    /**
     * @brief Constructs an empty FileInfo.
     */
    FileInfo() = default;

    /**
     * @brief Constructs a FileInfo for the specified path.
     * @param path The path to the file.
     */
    explicit FileInfo(const Path& path);

    /**
     * @brief Constructs a FileInfo for the specified path.
     * @param path The path to the file.
     */
    explicit FileInfo(const std::string& str);

    /**
     * @brief Constructs a FileInfo from a directory entry.
     * @param entry The directory entry.
     */
    explicit FileInfo(const fs::directory_entry& entry);

    /**
     * @brief Constructs a FileInfo from a directory entry.
     * @param entry The directory entry.
     */
    explicit FileInfo(fs::directory_entry&& entry);

    /**
     * @brief Copy constructor.
     */
    FileInfo(const FileInfo& other);

    /**
     * @brief Move constructor.
     */
    FileInfo(FileInfo&& other) noexcept;

    /**
     * @brief Assignment operator.
     */
    FileInfo& operator=(const FileInfo& other);

    /**
     * @brief Move assignment operator.
     */
    FileInfo& operator=(FileInfo&& other) noexcept;

    /**
     * @brief Destructor.
     */
    ~FileInfo() = default;

    /**
     * @brief Returns the file name.
     * @return The file name.
     */
    std::string fileName() const;

    /**
     * @brief Returns the base name (file name without extension).
     * @return The base name.
     */
    std::string baseName() const;

    /**
     * @brief Returns the file extension.
     * @return The file extension (without the dot).
     */
    std::string extension() const;

    /**
     * @brief Returns the file path.
     * @return The file path.
     */
    std::string filePath() const;

    /**
     * @brief Returns the absolute path.
     * @return The absolute path.
     */
    std::string absolutePath() const;

    /**
     * @brief Returns the absolute file path.
     * @return The absolute file path.
     */
    std::string absoluteFilePath() const;

    /**
     * @brief Returns whether the file exists.
     * @return True if the file exists, false otherwise.
     */
    bool exists() const;

    /**
     * @brief Returns whether the path is a file.
     * @return True if the path is a file, false otherwise.
     */
    bool isFile() const;

    /**
     * @brief Returns whether the path is a directory.
     * @return True if the path is a directory, false otherwise.
     */
    bool isDir() const;

    /**
     * @brief Returns whether the path is a symbolic link.
     * @return True if the path is a symbolic link, false otherwise.
     */
    bool isSymLink() const;

    /**
     * @brief Returns whether the file is hidden.
     * @return True if the file is hidden, false otherwise.
     */
    bool isHidden() const;

    /**
     * @brief Returns the file size.
     * @return The file size in bytes.
     */
    std::uintmax_t size() const;

    /**
     * @brief Returns the last modified time.
     * @return The last modified time.
     */
    std::chrono::system_clock::time_point lastModified() const;

    /**
     * @brief Returns the last accessed time.
     * @return The last accessed time.
     */
    std::chrono::system_clock::time_point lastAccessed() const;

    /**
     * @brief Returns the creation time.
     * @return The creation time.
     */
    std::chrono::system_clock::time_point created() const;

    /**
     * @brief Refreshes the file information.
     */
    void refresh();

    /**
     * @brief Sets the file path.
     * @param path The new file path.
     */
    void setFile(const Path& path);

    /**
     * @brief Sets the file path.
     * @param path The new file path.
     */
    void setFile(const std::string& path);

private:
#ifdef _WIN32
    DWORD getWindowsFileAttributes() const;
    bool isWindowsHiddenFile() const;
#endif

    fs::directory_entry m_entry;
};

} // namespace filesystem

#endif // FILEINFO_H
