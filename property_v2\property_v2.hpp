#pragma once

/**
 * @file property_v2.hpp
 * @brief 现代化属性系统主头文件
 * 
 * 这是属性系统的主要包含文件，提供了完整的属性系统功能。
 * 
 * ## 特性
 * 
 * - **现代C++20设计**：使用concepts、source_location等现代特性
 * - **类型安全**：编译时类型检查和约束
 * - **高性能**：零开销抽象和内联优化
 * - **易用性**：直观的API和丰富的便利函数
 * - **可扩展性**：模块化设计，支持自定义扩展
 * - **调试友好**：完整的错误信息和源位置追踪
 * 
 * ## 基本用法
 * 
 * ```cpp
 * #include "property_v2.hpp"
 * using namespace property_v2;
 * 
 * // 创建属性
 * property::property<int> x{42};
 * property::property<std::string> name{"Hello"};
 * 
 * // 读取和设置值
 * int value = x.value();
 * x.set_value(100);
 * 
 * // 创建绑定
 * property::property<int> y;
 * y.set_binding([&x]() { return x.value() * 2; });
 * 
 * // 观察变更
 * auto handler = x.on_value_changed([]() {
 *     std::cout << "x changed!" << std::endl;
 * });
 * ```
 * 
 * ## 高级用法
 * 
 * ```cpp
 * // 组合多个属性
 * auto sum_binding = property::combinators::combine(x, y, 
 *     [](int a, int b) { return a + b; });
 * 
 * // 映射属性值
 * auto string_binding = property::combinators::map(x, 
 *     [](int value) { return std::to_string(value); });
 * 
 * // 性能监控
 * support::performance_monitor::instance().enable();
 * auto stats = support::performance_monitor::instance().get_stats();
 * ```
 * 
 * <AUTHOR> System V2
 * @date 2025
 * @version 2.0.0
 */

// 核心模块
#include "core/concepts.hpp"
#include "core/type_traits.hpp"
#include "core/memory.hpp"
#include "core/utils.hpp"

// 绑定模块
#include "binding/binding_base.hpp"
#include "binding/property_binding.hpp"
#include "binding/binding_data.hpp"

// 观察者模块
#include "observer/observer_base.hpp"
#include "observer/change_handler.hpp"

// 属性模块
#include "property/property_data.hpp"
#include "property/property.hpp"
#include "property/property_utils.hpp"

// 支持模块
#include "support/error_handling.hpp"
#include "support/performance.hpp"

/**
 * @namespace property_v2
 * @brief 现代化属性系统的根命名空间
 * 
 * 包含所有属性系统的组件和功能。
 */
namespace property_v2 {

/**
 * @namespace property_v2::core
 * @brief 核心基础设施
 * 
 * 包含类型特征、概念、内存管理等基础组件。
 */

/**
 * @namespace property_v2::binding
 * @brief 属性绑定系统
 * 
 * 包含绑定创建、管理和求值的所有功能。
 */

/**
 * @namespace property_v2::observer
 * @brief 观察者模式实现
 * 
 * 包含属性观察者、变更处理器和通知器。
 */

/**
 * @namespace property_v2::property
 * @brief 主属性系统
 * 
 * 包含属性类、工具函数和便利操作。
 */

/**
 * @namespace property_v2::support
 * @brief 支持工具
 * 
 * 包含错误处理、性能监控等支持功能。
 */

// 版本信息
namespace version {
    constexpr int major = 2;
    constexpr int minor = 0;
    constexpr int patch = 0;
    constexpr const char* string = "2.0.0";
    constexpr const char* build_date = __DATE__;
    constexpr const char* build_time = __TIME__;
}

// 便利类型别名
namespace types {
    // 核心类型
    template<typename T>
    using property = property::property<T>;
    
    template<typename T>
    using property_binding = binding::property_binding<T>;
    
    using untyped_property_binding = binding::untyped_property_binding;
    using binding_error = binding::binding_error;
    using source_location = core::source_location;
    
    // 观察者类型
    template<typename F>
    using change_handler = observer::property_change_handler<F>;
    
    using notifier = observer::property_notifier;
    
    // 支持类型
    using error_handler = support::error_handler;
    using performance_stats = support::performance_stats;
}

// 便利函数
namespace functions {
    // 绑定创建
    using binding::make_property_binding;
    
    // 观察者创建
    using observer::make_change_handler;
    using observer::make_notifier;
    
    // 属性组合
    using property::combinators::combine;
    using property::combinators::map;
    using property::combinators::filter;
    
    // 错误处理
    using support::report_error;
    using support::report_exception;
    using support::report_binding_error;
}

// 便利宏
using PROPERTY_DEFAULT_BINDING_LOCATION = core::PROPERTY_DEFAULT_BINDING_LOCATION;

} // namespace property_v2

/**
 * @example basic_usage.cpp
 * 基本用法示例
 * 
 * 展示如何创建属性、设置值、创建绑定和观察变更。
 */

/**
 * @example advanced_bindings.cpp
 * 高级绑定示例
 * 
 * 展示复杂的绑定场景，包括多属性组合、条件绑定等。
 */

/**
 * @example performance_monitoring.cpp
 * 性能监控示例
 * 
 * 展示如何使用性能监控功能来分析属性系统的性能。
 */

/**
 * @example error_handling.cpp
 * 错误处理示例
 * 
 * 展示如何处理绑定错误和异常情况。
 */
