#pragma once

/**
 * @file error_handling.hpp
 * @brief 属性系统错误处理
 * 
 * 提供属性系统的错误处理机制，包括异常类型、错误报告和恢复策略。
 * 
 * <AUTHOR> System V2
 * @date 2025
 */

#include <stdexcept>
#include <string>
#include <string_view>
#include <functional>
#include <memory>
#include <vector>
#include "../core/utils.hpp"
#include "../binding/binding_base.hpp"

namespace property_v2::support {

/**
 * @brief 属性系统基础异常类
 */
class property_exception : public std::runtime_error {
public:
    /**
     * @brief 构造函数
     * @param message 错误消息
     * @param location 源位置
     */
    explicit property_exception(const std::string& message, 
                               const core::source_location& location = core::source_location::current())
        : std::runtime_error(message), location_(location) {}
    
    /**
     * @brief 获取源位置
     */
    const core::source_location& source_location() const noexcept { return location_; }
    
    /**
     * @brief 获取详细错误信息
     */
    virtual std::string detailed_message() const {
        return std::string(what()) + " at " + std::string(location_.file_name()) + 
               ":" + std::to_string(location_.line());
    }

private:
    core::source_location location_;
};

/**
 * @brief 绑定循环异常
 */
class binding_loop_exception : public property_exception {
public:
    explicit binding_loop_exception(const std::string& message = "Binding loop detected",
                                   const core::source_location& location = core::source_location::current())
        : property_exception(message, location) {}
};

/**
 * @brief 绑定求值异常
 */
class binding_evaluation_exception : public property_exception {
public:
    explicit binding_evaluation_exception(const std::string& message = "Binding evaluation failed",
                                         const core::source_location& location = core::source_location::current())
        : property_exception(message, location) {}
};

/**
 * @brief 类型不匹配异常
 */
class type_mismatch_exception : public property_exception {
public:
    explicit type_mismatch_exception(const std::string& message = "Type mismatch",
                                    const core::source_location& location = core::source_location::current())
        : property_exception(message, location) {}
};

/**
 * @brief 无效操作异常
 */
class invalid_operation_exception : public property_exception {
public:
    explicit invalid_operation_exception(const std::string& message = "Invalid operation",
                                        const core::source_location& location = core::source_location::current())
        : property_exception(message, location) {}
};

/**
 * @brief 错误处理策略枚举
 */
enum class error_handling_strategy {
    throw_exception,    ///< 抛出异常
    log_and_continue,   ///< 记录错误并继续
    ignore,             ///< 忽略错误
    custom              ///< 自定义处理
};

/**
 * @brief 错误处理器接口
 */
class error_handler {
public:
    virtual ~error_handler() = default;
    
    /**
     * @brief 处理绑定错误
     * @param error 绑定错误
     * @param location 源位置
     */
    virtual void handle_binding_error(const binding::binding_error& error,
                                     const core::source_location& location) = 0;
    
    /**
     * @brief 处理异常
     * @param exception 异常对象
     */
    virtual void handle_exception(const std::exception& exception) = 0;
    
    /**
     * @brief 处理一般错误
     * @param message 错误消息
     * @param location 源位置
     */
    virtual void handle_error(const std::string& message,
                             const core::source_location& location) = 0;
};

/**
 * @brief 默认错误处理器
 */
class default_error_handler : public error_handler {
public:
    /**
     * @brief 构造函数
     * @param strategy 错误处理策略
     */
    explicit default_error_handler(error_handling_strategy strategy = error_handling_strategy::throw_exception)
        : strategy_(strategy) {}
    
    /**
     * @brief 处理绑定错误
     */
    void handle_binding_error(const binding::binding_error& error,
                             const core::source_location& location) override {
        std::string message = "Binding error: " + error.description();
        handle_error_with_strategy(message, location);
    }
    
    /**
     * @brief 处理异常
     */
    void handle_exception(const std::exception& exception) override {
        std::string message = "Exception: " + std::string(exception.what());
        handle_error_with_strategy(message, core::source_location::current());
    }
    
    /**
     * @brief 处理一般错误
     */
    void handle_error(const std::string& message,
                     const core::source_location& location) override {
        handle_error_with_strategy(message, location);
    }
    
    /**
     * @brief 设置错误处理策略
     */
    void set_strategy(error_handling_strategy strategy) noexcept {
        strategy_ = strategy;
    }
    
    /**
     * @brief 设置自定义错误处理函数
     */
    void set_custom_handler(std::function<void(const std::string&, const core::source_location&)> handler) {
        custom_handler_ = std::move(handler);
    }

private:
    error_handling_strategy strategy_;
    std::function<void(const std::string&, const core::source_location&)> custom_handler_;
    
    void handle_error_with_strategy(const std::string& message, const core::source_location& location) {
        switch (strategy_) {
            case error_handling_strategy::throw_exception:
                throw property_exception(message, location);
                
            case error_handling_strategy::log_and_continue:
                std::cerr << "[ERROR] " << message << " at " << location.file_name() 
                         << ":" << location.line() << std::endl;
                break;
                
            case error_handling_strategy::ignore:
                // 什么都不做
                break;
                
            case error_handling_strategy::custom:
                if (custom_handler_) {
                    custom_handler_(message, location);
                }
                break;
        }
    }
};

/**
 * @brief 错误管理器
 * 
 * 全局错误处理管理。
 */
class error_manager {
public:
    /**
     * @brief 获取全局实例
     */
    static error_manager& instance() {
        static error_manager instance_;
        return instance_;
    }
    
    /**
     * @brief 设置错误处理器
     */
    void set_error_handler(std::unique_ptr<error_handler> handler) {
        handler_ = std::move(handler);
    }
    
    /**
     * @brief 获取错误处理器
     */
    error_handler* get_error_handler() const noexcept {
        return handler_.get();
    }
    
    /**
     * @brief 报告绑定错误
     */
    void report_binding_error(const binding::binding_error& error,
                             const core::source_location& location = core::source_location::current()) {
        if (handler_) {
            handler_->handle_binding_error(error, location);
        }
    }
    
    /**
     * @brief 报告异常
     */
    void report_exception(const std::exception& exception) {
        if (handler_) {
            handler_->handle_exception(exception);
        }
    }
    
    /**
     * @brief 报告一般错误
     */
    void report_error(const std::string& message,
                     const core::source_location& location = core::source_location::current()) {
        if (handler_) {
            handler_->handle_error(message, location);
        }
    }

private:
    error_manager() : handler_(std::make_unique<default_error_handler>()) {}
    
    std::unique_ptr<error_handler> handler_;
};

/**
 * @brief 错误报告便利函数
 */
inline void report_binding_error(const binding::binding_error& error,
                                const core::source_location& location = core::source_location::current()) {
    error_manager::instance().report_binding_error(error, location);
}

inline void report_exception(const std::exception& exception) {
    error_manager::instance().report_exception(exception);
}

inline void report_error(const std::string& message,
                        const core::source_location& location = core::source_location::current()) {
    error_manager::instance().report_error(message, location);
}

/**
 * @brief 错误处理作用域守卫
 * 
 * 在作用域内临时设置错误处理策略。
 */
class scoped_error_handler {
public:
    /**
     * @brief 构造函数
     * @param handler 临时错误处理器
     */
    explicit scoped_error_handler(std::unique_ptr<error_handler> handler)
        : previous_handler_(std::move(error_manager::instance().get_error_handler())) {
        error_manager::instance().set_error_handler(std::move(handler));
    }
    
    /**
     * @brief 析构函数，恢复之前的错误处理器
     */
    ~scoped_error_handler() {
        // 注意：这里简化了实现，实际应该保存和恢复完整的处理器
    }
    
    PROPERTY_DISABLE_COPY_MOVE(scoped_error_handler)

private:
    error_handler* previous_handler_;
};

} // namespace property_v2::support
