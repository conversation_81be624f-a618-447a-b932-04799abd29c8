#pragma once

/**
 * @file change_handler.hpp
 * @brief 属性变更处理器
 * 
 * 实现属性值变更时的回调处理机制。
 * 
 * <AUTHOR> System V2
 * @date 2025
 */

#include <functional>
#include <memory>
#include <utility>
#include "observer_base.hpp"
#include "../core/concepts.hpp"

namespace property_v2::observer {

/**
 * @brief 属性变更处理器模板类
 * 
 * 当属性值发生变更时，自动调用指定的处理函数。
 * 
 * @tparam F 处理函数类型
 */
template<typename F>
    requires core::Nullary_invocable<F>
class property_change_handler {
public:
    /**
     * @brief 构造函数
     * @param handler 处理函数
     */
    explicit property_change_handler(F handler) noexcept(std::is_nothrow_move_constructible_v<F>)
        : observer_(std::make_unique<property_observer>())
        , handler_(std::move(handler)) {
        
        observer_->set_change_handler([this]() {
            handler_();
        });
    }
    
    /**
     * @brief 构造函数（带属性绑定）
     * @tparam Property 属性类型
     * @param property 要观察的属性
     * @param handler 处理函数
     */
    template<typename Property>
        requires requires(Property p) { p.binding_data(); }
    property_change_handler(const Property& property, F handler) 
        noexcept(std::is_nothrow_move_constructible_v<F>)
        : property_change_handler(std::move(handler)) {
        set_source(property);
    }
    
    /**
     * @brief 移动构造函数
     */
    property_change_handler(property_change_handler&& other) noexcept
        : observer_(std::move(other.observer_))
        , handler_(std::move(other.handler_)) {}
    
    /**
     * @brief 禁用拷贝
     */
    PROPERTY_DISABLE_COPY(property_change_handler)
    
    /**
     * @brief 移动赋值运算符
     */
    property_change_handler& operator=(property_change_handler&& other) noexcept {
        if (this != &other) {
            observer_ = std::move(other.observer_);
            handler_ = std::move(other.handler_);
        }
        return *this;
    }
    
    /**
     * @brief 析构函数
     */
    ~property_change_handler() = default;
    
    /**
     * @brief 设置观察的属性
     * @tparam Property 属性类型
     * @param property 要观察的属性
     */
    template<typename Property>
        requires requires(Property p) { p.binding_data(); }
    void set_source(const Property& property) {
        if (observer_) {
            observer_->set_source(&property.binding_data());
        }
    }
    
    /**
     * @brief 手动触发处理函数
     */
    void trigger() const {
        if constexpr (std::is_invocable_v<F>) {
            handler_();
        }
    }
    
    /**
     * @brief 检查是否有效
     */
    bool is_valid() const noexcept {
        return observer_ != nullptr;
    }
    
    /**
     * @brief 断开观察
     */
    void disconnect() noexcept {
        observer_.reset();
    }

private:
    std::unique_ptr<property_observer> observer_;  ///< 观察者对象
    F handler_;                                    ///< 处理函数
};

/**
 * @brief 创建属性变更处理器的便利函数
 * @tparam F 处理函数类型
 * @param handler 处理函数
 * @return 变更处理器对象
 */
template<typename F>
    requires core::Nullary_invocable<F>
auto make_change_handler(F&& handler) {
    return property_change_handler<std::remove_cvref_t<F>>(std::forward<F>(handler));
}

/**
 * @brief 创建带属性绑定的变更处理器的便利函数
 * @tparam Property 属性类型
 * @tparam F 处理函数类型
 * @param property 要观察的属性
 * @param handler 处理函数
 * @return 变更处理器对象
 */
template<typename Property, typename F>
    requires requires(Property p) { p.binding_data(); } && core::Nullary_invocable<F>
auto make_change_handler(const Property& property, F&& handler) {
    return property_change_handler<std::remove_cvref_t<F>>(property, std::forward<F>(handler));
}

/**
 * @brief 通用属性通知器
 * 
 * 使用std::function的类型擦除版本，适用于需要运行时多态的场景。
 */
class property_notifier {
public:
    using handler_fn = std::function<void()>;
    
    /**
     * @brief 默认构造函数
     */
    property_notifier() = default;
    
    /**
     * @brief 构造函数
     * @param handler 处理函数
     */
    template<typename F>
        requires core::Nullary_invocable<F> && (!std::same_as<std::remove_cvref_t<F>, property_notifier>)
    explicit property_notifier(F&& handler)
        : observer_(std::make_unique<property_observer>())
        , handler_(std::forward<F>(handler)) {
        
        observer_->set_change_handler([this]() {
            if (handler_) {
                handler_();
            }
        });
    }
    
    /**
     * @brief 构造函数（带属性绑定）
     * @tparam Property 属性类型
     * @tparam F 处理函数类型
     * @param property 要观察的属性
     * @param handler 处理函数
     */
    template<typename Property, typename F>
        requires requires(Property p) { p.binding_data(); } && core::Nullary_invocable<F>
    property_notifier(const Property& property, F&& handler)
        : property_notifier(std::forward<F>(handler)) {
        set_source(property);
    }
    
    /**
     * @brief 移动构造函数
     */
    property_notifier(property_notifier&& other) noexcept
        : observer_(std::move(other.observer_))
        , handler_(std::move(other.handler_)) {}
    
    /**
     * @brief 禁用拷贝
     */
    PROPERTY_DISABLE_COPY(property_notifier)
    
    /**
     * @brief 移动赋值运算符
     */
    property_notifier& operator=(property_notifier&& other) noexcept {
        if (this != &other) {
            observer_ = std::move(other.observer_);
            handler_ = std::move(other.handler_);
        }
        return *this;
    }
    
    /**
     * @brief 析构函数
     */
    ~property_notifier() = default;
    
    /**
     * @brief 设置处理函数
     * @tparam F 处理函数类型
     * @param handler 处理函数
     */
    template<typename F>
        requires core::Nullary_invocable<F>
    void set_handler(F&& handler) {
        handler_ = std::forward<F>(handler);
    }
    
    /**
     * @brief 设置观察的属性
     * @tparam Property 属性类型
     * @param property 要观察的属性
     */
    template<typename Property>
        requires requires(Property p) { p.binding_data(); }
    void set_source(const Property& property) {
        if (observer_) {
            observer_->set_source(&property.binding_data());
        }
    }
    
    /**
     * @brief 手动触发处理函数
     */
    void trigger() const {
        if (handler_) {
            handler_();
        }
    }
    
    /**
     * @brief 检查是否有效
     */
    bool is_valid() const noexcept {
        return observer_ != nullptr && handler_ != nullptr;
    }
    
    /**
     * @brief 断开观察
     */
    void disconnect() noexcept {
        observer_.reset();
        handler_ = nullptr;
    }

private:
    std::unique_ptr<property_observer> observer_;  ///< 观察者对象
    handler_fn handler_;                           ///< 处理函数
};

/**
 * @brief 创建属性通知器的便利函数
 * @tparam F 处理函数类型
 * @param handler 处理函数
 * @return 通知器对象
 */
template<typename F>
    requires core::Nullary_invocable<F>
property_notifier make_notifier(F&& handler) {
    return property_notifier(std::forward<F>(handler));
}

/**
 * @brief 创建带属性绑定的通知器的便利函数
 * @tparam Property 属性类型
 * @tparam F 处理函数类型
 * @param property 要观察的属性
 * @param handler 处理函数
 * @return 通知器对象
 */
template<typename Property, typename F>
    requires requires(Property p) { p.binding_data(); } && core::Nullary_invocable<F>
property_notifier make_notifier(const Property& property, F&& handler) {
    return property_notifier(property, std::forward<F>(handler));
}

} // namespace property_v2::observer
