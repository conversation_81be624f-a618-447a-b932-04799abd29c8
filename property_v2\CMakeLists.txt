# CMakeLists.txt for Property System V2
# 现代化属性系统构建配置

cmake_minimum_required(VERSION 3.20)

# 项目信息
project(PropertySystemV2
    VERSION 2.0.0
    DESCRIPTION "Modern C++20 Property System"
    LANGUAGES CXX
)

# C++20 标准要求
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# 编译选项
if(MSVC)
    # MSVC 编译器选项
    add_compile_options(
        /W4                 # 警告级别4
        /WX                 # 警告视为错误
        /permissive-        # 严格标准符合性
        /Zc:__cplusplus     # 正确的 __cplusplus 宏值
        /utf-8              # UTF-8 源文件编码
    )
    
    # 发布版本优化
    add_compile_options($<$<CONFIG:Release>:/O2 /Ob2 /DNDEBUG>)
    
    # 调试版本选项
    add_compile_options($<$<CONFIG:Debug>:/Od /Zi /RTC1>)
    
else()
    # GCC/Clang 编译器选项
    add_compile_options(
        -Wall               # 基本警告
        -Wextra             # 额外警告
        -Werror             # 警告视为错误
        -Wpedantic          # 严格标准符合性
        -Wconversion        # 类型转换警告
        -Wsign-conversion   # 符号转换警告
        -Wunused            # 未使用变量警告
        -Wuninitialized     # 未初始化变量警告
        -Wshadow            # 变量遮蔽警告
    )
    
    # 发布版本优化
    add_compile_options($<$<CONFIG:Release>:-O3 -DNDEBUG -march=native>)
    
    # 调试版本选项
    add_compile_options($<$<CONFIG:Debug>:-O0 -g3 -fno-omit-frame-pointer>)
    
    # 地址消毒器（可选）
    option(ENABLE_ASAN "Enable AddressSanitizer" OFF)
    if(ENABLE_ASAN)
        add_compile_options(-fsanitize=address -fno-omit-frame-pointer)
        add_link_options(-fsanitize=address)
    endif()
    
    # 线程消毒器（可选）
    option(ENABLE_TSAN "Enable ThreadSanitizer" OFF)
    if(ENABLE_TSAN)
        add_compile_options(-fsanitize=thread)
        add_link_options(-fsanitize=thread)
    endif()
endif()

# 包含目录
include_directories(${CMAKE_CURRENT_SOURCE_DIR})

# 头文件列表
set(PROPERTY_V2_HEADERS
    # 主头文件
    property_v2.hpp
    
    # 核心模块
    core/concepts.hpp
    core/type_traits.hpp
    core/memory.hpp
    core/utils.hpp
    
    # 绑定模块
    binding/binding_base.hpp
    binding/property_binding.hpp
    binding/binding_data.hpp
    
    # 观察者模块
    observer/observer_base.hpp
    observer/change_handler.hpp
    
    # 属性模块
    property/property_data.hpp
    property/property.hpp
    property/property_utils.hpp
    
    # 支持模块
    support/error_handling.hpp
    support/performance.hpp
)

# 源文件列表（如果有实现文件）
set(PROPERTY_V2_SOURCES
    # 这里可以添加 .cpp 实现文件
    # 目前大部分是头文件模板实现
)

# 创建头文件库目标
add_library(property_v2 INTERFACE)

# 设置目标属性
target_include_directories(property_v2 INTERFACE
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}>
    $<INSTALL_INTERFACE:include>
)

target_compile_features(property_v2 INTERFACE cxx_std_20)

# 如果有源文件，创建静态库
if(PROPERTY_V2_SOURCES)
    add_library(property_v2_impl STATIC ${PROPERTY_V2_SOURCES})
    target_link_libraries(property_v2 INTERFACE property_v2_impl)
endif()

# 线程支持
find_package(Threads REQUIRED)
target_link_libraries(property_v2 INTERFACE Threads::Threads)

# 测试选项
option(BUILD_PROPERTY_V2_TESTS "Build Property V2 tests" ON)
if(BUILD_PROPERTY_V2_TESTS)
    enable_testing()
    add_subdirectory(tests)
endif()

# 示例选项
option(BUILD_PROPERTY_V2_EXAMPLES "Build Property V2 examples" ON)
if(BUILD_PROPERTY_V2_EXAMPLES)
    add_subdirectory(examples)
endif()

# 基准测试选项
option(BUILD_PROPERTY_V2_BENCHMARKS "Build Property V2 benchmarks" OFF)
if(BUILD_PROPERTY_V2_BENCHMARKS)
    add_subdirectory(benchmarks)
endif()

# 文档生成
option(BUILD_PROPERTY_V2_DOCS "Build Property V2 documentation" OFF)
if(BUILD_PROPERTY_V2_DOCS)
    find_package(Doxygen)
    if(DOXYGEN_FOUND)
        # Doxygen 配置
        set(DOXYGEN_IN ${CMAKE_CURRENT_SOURCE_DIR}/docs/Doxyfile.in)
        set(DOXYGEN_OUT ${CMAKE_CURRENT_BINARY_DIR}/Doxyfile)
        
        configure_file(${DOXYGEN_IN} ${DOXYGEN_OUT} @ONLY)
        
        add_custom_target(docs
            COMMAND ${DOXYGEN_EXECUTABLE} ${DOXYGEN_OUT}
            WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
            COMMENT "Generating API documentation with Doxygen"
            VERBATIM
        )
    else()
        message(WARNING "Doxygen not found, cannot build documentation")
    endif()
endif()

# 安装配置
include(GNUInstallDirs)

# 安装头文件
install(FILES ${PROPERTY_V2_HEADERS}
    DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}/property_v2
    COMPONENT Development
)

# 安装目标
install(TARGETS property_v2
    EXPORT property_v2_targets
    COMPONENT Development
)

# 导出目标
install(EXPORT property_v2_targets
    FILE property_v2_targets.cmake
    NAMESPACE property_v2::
    DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/property_v2
    COMPONENT Development
)

# 配置文件
include(CMakePackageConfigHelpers)

configure_package_config_file(
    ${CMAKE_CURRENT_SOURCE_DIR}/cmake/property_v2_config.cmake.in
    ${CMAKE_CURRENT_BINARY_DIR}/property_v2_config.cmake
    INSTALL_DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/property_v2
)

write_basic_package_version_file(
    ${CMAKE_CURRENT_BINARY_DIR}/property_v2_config_version.cmake
    VERSION ${PROJECT_VERSION}
    COMPATIBILITY SameMajorVersion
)

install(FILES
    ${CMAKE_CURRENT_BINARY_DIR}/property_v2_config.cmake
    ${CMAKE_CURRENT_BINARY_DIR}/property_v2_config_version.cmake
    DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/property_v2
    COMPONENT Development
)

# 打包配置
set(CPACK_PACKAGE_NAME "PropertySystemV2")
set(CPACK_PACKAGE_VERSION ${PROJECT_VERSION})
set(CPACK_PACKAGE_DESCRIPTION_SUMMARY ${PROJECT_DESCRIPTION})
set(CPACK_PACKAGE_VENDOR "Property System V2 Team")
set(CPACK_RESOURCE_FILE_LICENSE "${CMAKE_CURRENT_SOURCE_DIR}/LICENSE")
set(CPACK_RESOURCE_FILE_README "${CMAKE_CURRENT_SOURCE_DIR}/README.md")

include(CPack)

# 显示配置信息
message(STATUS "Property System V2 Configuration:")
message(STATUS "  Version: ${PROJECT_VERSION}")
message(STATUS "  C++ Standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  Build Type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  Build Tests: ${BUILD_PROPERTY_V2_TESTS}")
message(STATUS "  Build Examples: ${BUILD_PROPERTY_V2_EXAMPLES}")
message(STATUS "  Build Benchmarks: ${BUILD_PROPERTY_V2_BENCHMARKS}")
message(STATUS "  Build Documentation: ${BUILD_PROPERTY_V2_DOCS}")

if(ENABLE_ASAN)
    message(STATUS "  AddressSanitizer: Enabled")
endif()

if(ENABLE_TSAN)
    message(STATUS "  ThreadSanitizer: Enabled")
endif()
