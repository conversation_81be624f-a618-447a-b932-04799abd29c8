#include "../modern_property.h"
#include <iostream>
#include <vector>
#include <string>
#include <memory>
#include <algorithm>

/**
 * @file advanced_usage.cpp
 * @brief Advanced usage examples for the modern property system
 * 
 * This file demonstrates advanced features and complex usage scenarios
 * of the modern C++20 property binding system.
 */

using namespace modern_property;

/**
 * @brief Example class demonstrating property-based design
 */
class Rectangle {
public:
    // Properties with modern initialization
    property<double> width{0.0};
    property<double> height{0.0};
    property<double> area;
    property<double> perimeter;
    property<std::string> description;

    Rectangle() {
        setup_bindings();
    }
    
    Rectangle(double w, double h) : width{w}, height{h} {
        setup_bindings();
    }

private:
    void setup_bindings() {
        // Area is automatically calculated
        area.setBinding([this] {
            return width.value() * height.value();
        });
        
        // Perimeter is automatically calculated
        perimeter.setBinding([this] {
            return 2.0 * (width.value() + height.value());
        });
        
        // Description updates automatically
        description.setBinding([this] -> std::string {
            return "Rectangle " + std::to_string(width.value()) + 
                   "x" + std::to_string(height.value()) + 
                   " (area=" + std::to_string(area.value()) + ")";
        });
    }
};

/**
 * @brief Demonstrates property-based class design
 */
void demonstrate_property_classes() {
    std::cout << "\n=== Property-Based Class Design ===\n";
    
    Rectangle rect{10.0, 5.0};
    
    std::cout << "Initial: " << rect.description.value() << std::endl;
    std::cout << "Area: " << rect.area.value() << std::endl;
    std::cout << "Perimeter: " << rect.perimeter.value() << std::endl;
    
    // Modify dimensions - all dependent properties update automatically
    rect.width = 15.0;
    std::cout << "\nAfter width change: " << rect.description.value() << std::endl;
    
    rect.height = 8.0;
    std::cout << "After height change: " << rect.description.value() << std::endl;
    
    // Add observers to track changes
    rect.area.onValueChanged([](const double& old_val, const double& new_val) {
        std::cout << "Area changed from " << old_val << " to " << new_val << std::endl;
    });
    
    rect.width = 20.0;  // Triggers area change notification
}

/**
 * @brief Demonstrates working with collections of properties
 */
void demonstrate_property_collections() {
    std::cout << "\n=== Property Collections ===\n";
    
    // Create a collection of properties
    std::vector<property<int>> values;
    for (int i = 0; i < 5; ++i) {
        values.emplace_back(i * 10);
    }
    
    // Create properties that depend on the collection
    auto sum = utils::make_bound_property([&values] {
        int total = 0;
        for (const auto& prop : values) {
            total += prop.value();
        }
        return total;
    });
    
    auto average = utils::make_bound_property([&values, &sum] {
        return values.empty() ? 0.0 : static_cast<double>(sum.value()) / values.size();
    });
    
    auto max_value = utils::make_bound_property([&values] {
        if (values.empty()) return 0;
        int max_val = values[0].value();
        for (const auto& prop : values) {
            max_val = std::max(max_val, prop.value());
        }
        return max_val;
    });
    
    std::cout << "Initial collection:" << std::endl;
    for (size_t i = 0; i < values.size(); ++i) {
        std::cout << "  values[" << i << "] = " << values[i].value() << std::endl;
    }
    std::cout << "Sum: " << sum.value() << std::endl;
    std::cout << "Average: " << average.value() << std::endl;
    std::cout << "Max: " << max_value.value() << std::endl;
    
    // Modify collection values
    values[2] = 100;
    values[4] = 200;
    
    std::cout << "\nAfter modifications:" << std::endl;
    std::cout << "Sum: " << sum.value() << std::endl;
    std::cout << "Average: " << average.value() << std::endl;
    std::cout << "Max: " << max_value.value() << std::endl;
}

/**
 * @brief Demonstrates complex type usage with properties
 */
void demonstrate_complex_types() {
    std::cout << "\n=== Complex Type Properties ===\n";
    
    // Properties with complex types
    property<std::vector<int>> numbers{{1, 2, 3, 4, 5}};
    property<std::string> text{"Hello, World!"};
    
    // Binding that works with complex types
    auto vector_stats = utils::make_bound_property([&numbers] -> std::string {
        const auto& vec = numbers.value();
        if (vec.empty()) return "Empty vector";
        
        int sum = 0;
        for (int val : vec) {
            sum += val;
        }
        double avg = static_cast<double>(sum) / vec.size();
        
        return "Vector size: " + std::to_string(vec.size()) + 
               ", sum: " + std::to_string(sum) + 
               ", average: " + std::to_string(avg);
    });
    
    auto text_info = utils::make_bound_property([&text] -> std::string {
        const auto& str = text.value();
        return "Text length: " + std::to_string(str.length()) + 
               ", words: " + std::to_string(std::count(str.begin(), str.end(), ' ') + 1);
    });
    
    std::cout << "Vector stats: " << vector_stats.value() << std::endl;
    std::cout << "Text info: " << text_info.value() << std::endl;
    
    // Modify complex properties
    auto vec = numbers.value();
    vec.push_back(6);
    vec.push_back(7);
    numbers = vec;
    
    text = "Modern C++20 Property System is awesome!";
    
    std::cout << "\nAfter modifications:" << std::endl;
    std::cout << "Vector stats: " << vector_stats.value() << std::endl;
    std::cout << "Text info: " << text_info.value() << std::endl;
}

/**
 * @brief Demonstrates conditional bindings and complex logic
 */
void demonstrate_conditional_bindings() {
    std::cout << "\n=== Conditional Bindings ===\n";
    
    property<bool> is_enabled{true};
    property<double> base_value{100.0};
    property<double> multiplier{1.5};
    
    // Conditional binding based on enabled state
    auto computed_value = utils::make_bound_property([&] {
        if (is_enabled.value()) {
            return base_value.value() * multiplier.value();
        } else {
            return 0.0;
        }
    });
    
    // Status description with complex logic
    auto status = utils::make_bound_property([&] -> std::string {
        if (!is_enabled.value()) {
            return "DISABLED";
        }
        
        double value = computed_value.value();
        if (value < 50) return "LOW";
        if (value < 150) return "MEDIUM";
        if (value < 300) return "HIGH";
        return "VERY HIGH";
    });
    
    std::cout << "Enabled: " << std::boolalpha << is_enabled.value() << std::endl;
    std::cout << "Computed value: " << computed_value.value() << std::endl;
    std::cout << "Status: " << status.value() << std::endl;
    
    // Test different states
    is_enabled = false;
    std::cout << "\nAfter disabling:" << std::endl;
    std::cout << "Computed value: " << computed_value.value() << std::endl;
    std::cout << "Status: " << status.value() << std::endl;
    
    is_enabled = true;
    multiplier = 4.0;
    std::cout << "\nAfter re-enabling with higher multiplier:" << std::endl;
    std::cout << "Computed value: " << computed_value.value() << std::endl;
    std::cout << "Status: " << status.value() << std::endl;
}

/**
 * @brief Demonstrates performance optimization techniques
 */
void demonstrate_performance_optimizations() {
    std::cout << "\n=== Performance Optimizations ===\n";
    
    // Use batched updates for better performance
    property<int> x{0}, y{0}, z{0};
    
    auto sum = utils::make_bound_property([&] {
        std::cout << "Computing sum..." << std::endl;
        return x.value() + y.value() + z.value();
    });
    
    // Add observer to see when computation happens
    sum.onValueChanged([] {
        std::cout << "Sum changed!" << std::endl;
    });
    
    std::cout << "Without batching (triggers 3 computations):" << std::endl;
    x = 10;
    y = 20;
    z = 30;
    std::cout << "Final sum: " << sum.value() << std::endl;
    
    std::cout << "\nWith batching (triggers 1 computation):" << std::endl;
    {
        MODERN_PROPERTY_UPDATE_GROUP();
        x = 100;
        y = 200;
        z = 300;
        std::cout << "All values set, but computation deferred..." << std::endl;
    }
    std::cout << "Final sum: " << sum.value() << std::endl;
}

/**
 * @brief Demonstrates custom property types and specializations
 */
void demonstrate_custom_types() {
    std::cout << "\n=== Custom Type Properties ===\n";
    
    // Custom struct as property type
    struct Point {
        double x, y;
        
        Point(double x = 0, double y = 0) : x(x), y(y) {}
        
        bool operator==(const Point& other) const {
            return x == other.x && y == other.y;
        }
        
        Point operator+(const Point& other) const {
            return {x + other.x, y + other.y};
        }
        
        double distance_from_origin() const {
            return std::sqrt(x * x + y * y);
        }
    };
    
    property<Point> point1{{3.0, 4.0}};
    property<Point> point2{{1.0, 2.0}};
    
    // Binding with custom types
    auto sum_point = utils::make_bound_property([&] {
        return point1.value() + point2.value();
    });
    
    auto distance = utils::make_bound_property([&] {
        return sum_point.value().distance_from_origin();
    });
    
    std::cout << "Point1: (" << point1.value().x << ", " << point1.value().y << ")" << std::endl;
    std::cout << "Point2: (" << point2.value().x << ", " << point2.value().y << ")" << std::endl;
    std::cout << "Sum: (" << sum_point.value().x << ", " << sum_point.value().y << ")" << std::endl;
    std::cout << "Distance from origin: " << distance.value() << std::endl;
    
    // Modify points
    point1 = {5.0, 12.0};
    std::cout << "\nAfter point1 change:" << std::endl;
    std::cout << "Sum: (" << sum_point.value().x << ", " << sum_point.value().y << ")" << std::endl;
    std::cout << "Distance from origin: " << distance.value() << std::endl;
}

/**
 * @brief Main function for advanced examples
 */
int main() {
    std::cout << "Advanced Modern Property System Examples\n";
    std::cout << "========================================\n";
    
    try {
        demonstrate_property_classes();
        demonstrate_property_collections();
        demonstrate_complex_types();
        demonstrate_conditional_bindings();
        demonstrate_performance_optimizations();
        demonstrate_custom_types();
        
        std::cout << "\n=== All advanced demonstrations completed successfully! ===\n";
        
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
} 