﻿#include "path.h"

#include <filesystem>
#include <algorithm>

#ifdef _WIN32
#include <windows.h>
#endif

namespace fs = std::filesystem;
namespace filesystem {

Path::Path(const std::string& path)
    : m_path(stringToWString(path))
{
    // Normalize path separators to forward slashes
    std::replace(m_path.begin(), m_path.end(), L'\\', L'/');
}

Path::Path(const std::wstring& path) : m_path(path)
{
    // Normalize path separators to forward slashes
    std::replace(m_path.begin(), m_path.end(), L'\\', L'/');
}

Path::Path(const Path& other) : m_path(other.m_path) {}

Path::Path(Path&& other) noexcept : m_path(std::move(other.m_path)) {}

Path& Path::operator=(const Path& other)
{
    if (this != &other) {
        m_path = other.m_path;
    }
    return *this;
}

Path& Path::operator=(Path&& other) noexcept
{
    if (this != &other) {
        m_path = std::move(other.m_path);
    }
    return *this;
}

std::string Path::toString() const
{
    return wstringToString(m_path);
}

std::wstring Path::toWString() const
{
    return m_path;
}

std::string Path::toNativeString() const
{
    return wstringToString(toNativeWString());
}

std::wstring Path::toNativeWString() const
{
#ifdef _WIN32
    std::wstring result = m_path;
    std::replace(result.begin(), result.end(), L'/', L'\\');
    return result;
#else
    return m_path;
#endif
}

bool Path::isAbsolute() const {
    return fs::path(m_path).is_absolute();
}

bool Path::isRelative() const {
    return !isAbsolute();
}

std::string Path::fileName() const {
    return fs::path(m_path).filename().string();
}

std::string Path::baseName() const {
    fs::path p(m_path);
    std::string filename = p.filename().string();
    size_t lastDot = filename.find_last_of('.');
    if (lastDot == std::string::npos) {
        return filename;
    }
    return filename.substr(0, lastDot);
}

std::string Path::extension() const {
    std::string ext = fs::path(m_path).extension().string();
    if (!ext.empty() && ext[0] == '.') {
        return ext.substr(1);
    }
    return ext;
}

Path Path::parentPath() const {
    return Path(fs::path(m_path).parent_path().string());
}

bool Path::exists() const {
    return fs::exists(fs::path(m_path));
}

bool Path::isFile() const
{
    return fs::is_regular_file(fs::path(m_path));
}

bool Path::isDirectory() const
{
    return fs::is_directory(fs::path(m_path));
}

Path Path::join(const Path& other) const
{
    fs::path result = fs::path(m_path) / fs::path(other.m_path);
    return Path(result.string());
}

Path Path::join(const std::string& other) const
{
    fs::path result = fs::path(m_path) / fs::path(other);
    return Path(result.string());
}

Path Path::normalize() const
{
    fs::path result = fs::path(m_path).lexically_normal();
    return Path(result.string());
}

Path Path::makeAbsolute() const
{
    if (isAbsolute()) {
        return *this;
    }
    fs::path result = fs::absolute(fs::path(m_path));
    return Path(result.string());
}

Path Path::makeRelative(const Path& base) const
{
    fs::path p = fs::path(m_path);
    fs::path b = fs::path(base.m_path);
    
    // Make sure both paths are absolute
    if (!p.is_absolute()) {
        p = fs::absolute(p);
    }
    if (!b.is_absolute()) {
        b = fs::absolute(b);
    }
    
    // Try to make p relative to b
    fs::path result = fs::relative(p, b);
    return Path(result.string());
}

Path Path::correctCase() const
{
#ifdef _WIN32
    // Windows implementation
    if (!exists()) {
        return *this;
    }
    
    WIN32_FIND_DATAA findData;
    HANDLE hFind = FindFirstFileA(toNativeString().c_str(), &findData);
    if (hFind == INVALID_HANDLE_VALUE) {
        return *this;
    }
    
    FindClose(hFind);
    
    // Get the correct case for the last component
    std::string correctName = findData.cFileName;
    
    // For the parent path, recursively correct the case
    if (parentPath().toString() != ".") {
        return parentPath().correctCase().join(correctName);
    }
    
    return Path(correctName);
#else
    // Case-sensitive filesystems don't need correction
    return *this;
#endif
}

std::vector<std::string> Path::components() const
{
    std::vector<std::string> result;
    fs::path p = fs::path(m_path);
    
    for (const auto& component : p) {
        result.push_back(component.string());
    }
    
    return result;
}

std::string Path::root() const
{
    return fs::path(m_path).root_name().string();
}

std::string Path::drive() const
{
#ifdef _WIN32
    if (m_path.size() >= 2 && m_path[1] == L':') {
        return wstringToString(m_path.substr(0, 2));
    }
#endif
    return "";
}

bool Path::isUncPath() const
{
#ifdef _WIN32
    return m_path.size() >= 2 && m_path[0] == L'/' && m_path[1] == L'/';
#else
    return false;
#endif
}

// String conversion helper methods
std::wstring Path::stringToWString(const std::string& str) {
#ifdef _WIN32
    if (str.empty()) return std::wstring();

    // Get the required size for the buffer
    int size = MultiByteToWideChar(CP_UTF8, 0, str.c_str(), -1, nullptr, 0);
    if (size == 0) {
        return std::wstring();
    }

    // Allocate the buffer and convert
    std::vector<wchar_t> buffer(size);
    if (MultiByteToWideChar(CP_UTF8, 0, str.c_str(), -1, buffer.data(), size) == 0) {
        return std::wstring();
    }

    // Return as wstring (excluding null terminator)
    return std::wstring(buffer.data(), buffer.size() - 1);
#else
    // Use standard C++ conversion for non-Windows platforms
    std::wstring_convert<std::codecvt_utf8<wchar_t>> converter;
    return converter.from_bytes(str);
#endif
}

std::string Path::wstringToString(const std::wstring& wstr) {
#ifdef _WIN32
    if (wstr.empty()) return std::string();

    // Get the required size for the buffer
    int size = WideCharToMultiByte(CP_UTF8, 0, wstr.c_str(), -1, nullptr, 0, nullptr, nullptr);
    if (size == 0) {
        return std::string();
    }

    // Allocate the buffer and convert
    std::vector<char> buffer(size);
    if (WideCharToMultiByte(CP_UTF8, 0, wstr.c_str(), -1, buffer.data(), size, nullptr, nullptr) == 0) {
        return std::string();
    }

    // Return as string (excluding null terminator)
    return std::string(buffer.data(), buffer.size() - 1);
#else
    // Use standard C++ conversion for non-Windows platforms
    std::wstring_convert<std::codecvt_utf8<wchar_t>> converter;
    return converter.to_bytes(wstr);
#endif
}

} // namespace filesystem
