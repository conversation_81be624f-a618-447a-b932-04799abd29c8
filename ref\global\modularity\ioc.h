#ifndef MODULARITY_IOC_H
#define MODULARITY_IOC_H

#include <iostream>
#include <memory>
#include <mutex>

#include "modulesioc.h"

#define INJECT(Interface, getter) \
private: \
    mutable std::shared_ptr<Interface> m_##getter = nullptr; \
public: \
    std::shared_ptr<Interface> getter() const {  \
        if (!m_##getter) { \
            static std::mutex getter##mutex; \
            const std::lock_guard<std::mutex> getter##lock(getter##mutex); \
            if (!m_##getter) { \
                static const std::string_view sig(FUNC_SIG); \
                m_##getter = modularity::ioc()->resolve<Interface>(modularity::moduleNameBySig(sig), sig); \
            } \
        } \
        if (!m_##getter) { std::cout << "ioc: Canot get module: " << #getter << std::endl; } \
        return m_##getter; \
    } \
    void set##getter(std::shared_ptr<Interface> impl) { m_##getter = impl; } \

#define INJECT_STATIC(Interface, getter) \
public: \
    static std::shared_ptr<Interface>& getter() {  \
        static std::shared_ptr<Interface> s_##getter = nullptr; \
        if (!s_##getter) { \
            static std::mutex getter##mutex; \
            const std::lock_guard<std::mutex> getter##lock(getter##mutex); \
            if (!s_##getter) { \
                static const std::string_view sig(FUNC_SIG); \
                s_##getter = modularity::ioc()->resolve<Interface>(modularity::moduleNameBySig(sig), sig); \
            } \
        } \
        return s_##getter; \
    } \
    static void set##getter(std::shared_ptr<Interface> impl) { \
        std::shared_ptr<Interface>& s_##getter = getter(); \
        s_##getter = impl; \
    } \

namespace modularity {
inline ModulesIoC* ioc()
{
    return ModulesIoC::instance();
}

template<class I>
class Inject
{
public:
    Inject(const std::string_view& module = std::string_view())
        : m_module(module) {}

    const std::shared_ptr<I>& get() const
    {
        if (!m_i) {
            static std::mutex mutex;
            const std::lock_guard<std::mutex> lock(mutex);
            if (!m_i) {
                m_i = ioc()->resolve<I>(m_module);
            }
        }
        if (!m_i) {
            std::cout << "ioc: Canot get module: " << m_module << std::endl;
        }
        return m_i;
    }

    void set(std::shared_ptr<I> impl)
    {
        m_i = impl;
    }

    I* operator()() const
    {
        return get().get();
    }

private:
    std::string_view m_module;
    mutable std::shared_ptr<I> m_i = nullptr;
};
}

#endif // IOC_H
