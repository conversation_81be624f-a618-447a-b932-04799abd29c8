#include "../modern_property.h"
#include <iostream>
#include <cassert>

/**
 * @file basic_usage.cpp
 * @brief Basic usage examples for the modern property system
 * 
 * This file demonstrates the core functionality of the modern C++20
 * property binding system, showing API compatibility with the original
 * system while highlighting modern improvements.
 */

using namespace modern_property;

/**
 * @brief Demonstrates basic property creation and usage
 */
void demonstrate_basic_properties() {
    std::cout << "\n=== Basic Property Usage ===\n";
    
    // Create properties with initial values
    property<int> width{100};
    property<int> height{50};
    
    std::cout << "Width: " << width.value() << std::endl;
    std::cout << "Height: " << height.value() << std::endl;
    
    // Modify values using setValue (original API compatibility)
    width.setValue(200);
    height.setValue(75);
    
    std::cout << "After setValue - Width: " << width.value() 
              << ", Height: " << height.value() << std::endl;
    
    // Modern assignment operator usage
    width = 300;
    height = 100;
    
    std::cout << "After assignment - Width: " << width.value() 
              << ", Height: " << height.value() << std::endl;
    
    // Implicit conversion to value type
    int w = width;  // Uses operator const_param_type()
    int h = height;
    std::cout << "Implicit conversion - w: " << w << ", h: " << h << std::endl;
}

/**
 * @brief Demonstrates property binding functionality
 */
void demonstrate_property_binding() {
    std::cout << "\n=== Property Binding ===\n";
    
    // Create source properties
    property<int> width{100};
    property<int> height{50};
    property<int> area;
    
    // Set up binding using lambda (original API compatibility)
    area.setBinding([&] { 
        return width.value() * height.value(); 
    });
    
    std::cout << "Initial area: " << area.value() << std::endl;
    assert(area.value() == 5000);
    
    // Change source properties - area updates automatically
    width = 200;
    std::cout << "After width change - area: " << area.value() << std::endl;
    assert(area.value() == 10000);
    
    height = 75;
    std::cout << "After height change - area: " << area.value() << std::endl;
    assert(area.value() == 15000);
    
    // Check binding status
    std::cout << "Area has binding: " << std::boolalpha << area.hasBinding() << std::endl;
    
    // Remove binding and set manual value
    area.removeBinding();
    area = 99999;
    std::cout << "After removing binding - area: " << area.value() << std::endl;
    std::cout << "Area has binding: " << std::boolalpha << area.hasBinding() << std::endl;
}

/**
 * @brief Demonstrates modern binding creation with type deduction
 */
void demonstrate_modern_binding() {
    std::cout << "\n=== Modern Binding Features ===\n";
    
    // Create properties using modern factory functions
    auto x = utils::make_property(10.0);
    auto y = utils::make_property(20.0);
    
    // Create bound property with automatic type deduction
    auto distance = utils::make_bound_property([&] {
        return std::sqrt(x.value() * x.value() + y.value() * y.value());
    });
    
    std::cout << "Initial distance: " << distance.value() << std::endl;
    
    // Using convenient macros
    MODERN_PROPERTY_DECLARE(radius, double, 5.0);
    MODERN_PROPERTY_BIND(circumference, [&] { return 2.0 * 3.14159 * radius.value(); });
    
    std::cout << "Circumference: " << circumference.value() << std::endl;
    
    radius = 10.0;
    std::cout << "After radius change - circumference: " << circumference.value() << std::endl;
}

/**
 * @brief Demonstrates observer functionality
 */
void demonstrate_observers() {
    std::cout << "\n=== Observer System ===\n";
    
    property<int> temperature{20};
    
    // Add simple change observer (original API compatibility)
    temperature.onValueChanged([] {
        std::cout << "Temperature changed!" << std::endl;
    });
    
    // Add high-priority observer
    temperature.onValueChanged([] {
        std::cout << "High priority: Temperature alert!" << std::endl;
    }, observer_priority::high);
    
    // Add typed observer with old/new value access
    temperature.onValueChanged([](const int& old_val, const int& new_val) {
        std::cout << "Temperature changed from " << old_val 
                  << " to " << new_val << std::endl;
    });
    
    std::cout << "Observer count: " << temperature.observerCount() << std::endl;
    
    // Trigger notifications
    std::cout << "\nChanging temperature to 25:" << std::endl;
    temperature = 25;
    
    std::cout << "\nChanging temperature to 30:" << std::endl;
    temperature = 30;
}

/**
 * @brief Demonstrates batched notifications
 */
void demonstrate_batched_notifications() {
    std::cout << "\n=== Batched Notifications ===\n";
    
    property<int> x{0};
    property<int> y{0};
    
    // Add observers
    x.onValueChanged([] { std::cout << "X changed" << std::endl; });
    y.onValueChanged([] { std::cout << "Y changed" << std::endl; });
    
    std::cout << "Without batching:" << std::endl;
    x = 10;  // Immediate notification
    y = 20;  // Immediate notification
    
    std::cout << "\nWith batching:" << std::endl;
    {
        MODERN_PROPERTY_UPDATE_GROUP();  // Begin batch
        x = 100;  // Notification queued
        y = 200;  // Notification queued
        std::cout << "Inside batch - no notifications yet" << std::endl;
    }  // End batch - all notifications sent here
    std::cout << "After batch completed" << std::endl;
}

/**
 * @brief Demonstrates complex binding chains
 */
void demonstrate_binding_chains() {
    std::cout << "\n=== Complex Binding Chains ===\n";
    
    // Create a chain of dependent properties
    property<double> base{10.0};
    
    auto squared = utils::make_bound_property([&] {
        return base.value() * base.value();
    });
    
    auto cubed = utils::make_bound_property([&] {
        return squared.value() * base.value();
    });
    
    auto sum = utils::make_bound_property([&] {
        return base.value() + squared.value() + cubed.value();
    });
    
    std::cout << "Base: " << base.value() << std::endl;
    std::cout << "Squared: " << squared.value() << std::endl;
    std::cout << "Cubed: " << cubed.value() << std::endl;
    std::cout << "Sum: " << sum.value() << std::endl;
    
    // Change base value - all dependent properties update
    base = 3.0;
    
    std::cout << "\nAfter base = 3.0:" << std::endl;
    std::cout << "Base: " << base.value() << std::endl;
    std::cout << "Squared: " << squared.value() << std::endl;
    std::cout << "Cubed: " << cubed.value() << std::endl;
    std::cout << "Sum: " << sum.value() << std::endl;
    
    // Show dependency information
    std::cout << "\nDependency information:" << std::endl;
    std::cout << "Sum depends on " << sum.dependencies().size() << " properties" << std::endl;
}

/**
 * @brief Demonstrates error handling and debugging features
 */
void demonstrate_error_handling() {
    std::cout << "\n=== Error Handling & Debugging ===\n";
    
    property<int> value{10};
    
    // Create a binding that might have issues
    value.setBinding([] {
        // This binding always returns the same value
        // In a real scenario, this might involve complex calculations
        // that could fail or have circular dependencies
        return 42;
    });
    
    std::cout << "Value with binding: " << value.value() << std::endl;
    
    // Demonstrate binding removal and manual override
    auto old_binding = value.removeBinding();
    std::cout << "Binding removed successfully" << std::endl;
    
    value = 100;
    std::cout << "Manual value after binding removal: " << value.value() << std::endl;
    
    // Show type safety
    std::cout << "Property type: " << value.type_name() << std::endl;
}

/**
 * @brief Main function demonstrating all features
 */
int main() {
    std::cout << "Modern Property System Example\n";
    std::cout << "Version: " << version::string << std::endl;
    std::cout << version::build_info << std::endl;
    
    try {
        demonstrate_basic_properties();
        demonstrate_property_binding();
        demonstrate_modern_binding();
        demonstrate_observers();
        demonstrate_batched_notifications();
        demonstrate_binding_chains();
        demonstrate_error_handling();
        
        std::cout << "\n=== All demonstrations completed successfully! ===\n";
        
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
} 