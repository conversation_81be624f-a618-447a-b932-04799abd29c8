#pragma once

/**
 * @file concepts.hpp
 * @brief 现代化属性系统的类型约束概念定义
 * 
 * 使用 C++20 concepts 替代传统的 SFINAE，提供更清晰的类型约束和更好的编译错误信息。
 * 
 * <AUTHOR> System V2
 * @date 2025
 */

#include <concepts>
#include <type_traits>
#include <functional>
#include <memory>

namespace property_v2::core {

/**
 * @brief 检查类型是否具有相等比较运算符
 * @tparam T 要检查的类型
 */
template<typename T>
concept Equality_comparable = requires(const T& a, const T& b) {
    { a == b } -> std::convertible_to<bool>;
};

/**
 * @brief 检查类型是否可以被解引用
 * @tparam T 要检查的类型
 */
template<typename T>
concept Dereferenceable = requires(T t) {
    { *t };
    { t.operator->() };
};

/**
 * @brief 检查类型是否为智能指针
 * @tparam T 要检查的类型
 */
template<typename T>
concept Smart_pointer = requires {
    typename T::element_type;
} && (
    std::same_as<T, std::shared_ptr<typename T::element_type>> ||
    std::same_as<T, std::unique_ptr<typename T::element_type>> ||
    std::same_as<T, std::weak_ptr<typename T::element_type>>
);

/**
 * @brief 检查类型是否为可调用对象
 * @tparam F 函数类型
 * @tparam Args 参数类型
 */
template<typename F, typename... Args>
concept Invocable = std::invocable<F, Args...>;

/**
 * @brief 检查类型是否为可调用对象且返回指定类型
 * @tparam F 函数类型
 * @tparam R 返回类型
 * @tparam Args 参数类型
 */
template<typename F, typename R, typename... Args>
concept Invocable_r = std::invocable<F, Args...> && 
                      std::convertible_to<std::invoke_result_t<F, Args...>, R>;

/**
 * @brief 检查类型是否为无参数可调用对象
 * @tparam F 函数类型
 */
template<typename F>
concept Nullary_invocable = Invocable<F>;

/**
 * @brief 检查类型是否为无参数可调用对象且返回指定类型
 * @tparam F 函数类型
 * @tparam R 返回类型
 */
template<typename F, typename R>
concept Nullary_invocable_r = Invocable_r<F, R>;

/**
 * @brief 检查类型是否可移动构造
 * @tparam T 要检查的类型
 */
template<typename T>
concept Move_constructible = std::move_constructible<T>;

/**
 * @brief 检查类型是否可复制构造
 * @tparam T 要检查的类型
 */
template<typename T>
concept Copy_constructible = std::copy_constructible<T>;

/**
 * @brief 检查类型是否可移动赋值
 * @tparam T 要检查的类型
 */
template<typename T>
concept Move_assignable = std::move_constructible<T> && std::assignable_from<T&, T>;

/**
 * @brief 检查类型是否可复制赋值
 * @tparam T 要检查的类型
 */
template<typename T>
concept Copy_assignable = Copy_constructible<T> && std::assignable_from<T&, const T&>;

/**
 * @brief 检查类型是否为算术类型
 * @tparam T 要检查的类型
 */
template<typename T>
concept Arithmetic = std::is_arithmetic_v<T>;

/**
 * @brief 检查类型是否为枚举类型
 * @tparam T 要检查的类型
 */
template<typename T>
concept Enumeration = std::is_enum_v<T>;

/**
 * @brief 检查类型是否为指针类型
 * @tparam T 要检查的类型
 */
template<typename T>
concept Pointer = std::is_pointer_v<T>;

/**
 * @brief 检查类型是否应该使用引用传递
 * 
 * 对于小型类型（算术类型、枚举、指针），使用值传递更高效。
 * 对于大型类型，使用引用传递避免不必要的拷贝。
 * 
 * @tparam T 要检查的类型
 */
template<typename T>
concept Use_reference_semantics = !(Arithmetic<T> || Enumeration<T> || Pointer<T>);

/**
 * @brief 检查类型是否为属性数据类型
 * @tparam T 要检查的类型
 */
template<typename T>
concept Property_data_type = requires {
    typename T::value_type;
    typename T::parameter_type;
} && requires(T t, typename T::parameter_type p) {
    { t.value_bypassing_bindings() } -> std::convertible_to<typename T::parameter_type>;
    t.set_value_bypassing_bindings(p);
};

/**
 * @brief 检查类型是否为绑定类型
 * @tparam T 要检查的类型
 */
template<typename T>
concept Binding_type = requires(T t) {
    { t.is_null() } -> std::convertible_to<bool>;
    typename T::value_type;
};

/**
 * @brief 检查类型是否为观察者类型
 * @tparam T 要检查的类型
 */
template<typename T>
concept Observer_type = requires(T t) {
    t.notify();
} && Move_constructible<T>;

} // namespace property_v2::core
