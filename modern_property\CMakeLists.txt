cmake_minimum_required(VERSION 3.20)

project(ModernPropertySystem
    VERSION 1.0.0
    DESCRIPTION "Modern C++20 Property Binding System"
    LANGUAGES CXX
)

# Require C++20
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Compiler-specific options
if(MSVC)
    add_compile_options(/W4 /permissive-)
    # Enable C++20 features
    add_compile_options(/std:c++20)
    # Disable specific warnings for compatibility
    add_compile_options(/wd4996) # Disable deprecated warnings
else()
    add_compile_options(-Wall -Wextra -Wpedantic)
    # Enable C++20 features for GCC/Clang
    add_compile_options(-std=c++20)
    # Enable concepts and ranges
    if(CMAKE_CXX_COMPILER_ID STREQUAL "GNU")
        add_compile_options(-fconcepts-ts)
    endif()
endif()

# Build type configuration
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Configure build types
set(CMAKE_CXX_FLAGS_DEBUG "-g -O0 -DDEBUG")
set(CMAKE_CXX_FLAGS_RELEASE "-O3 -DNDEBUG")

# Include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR})

# Core library
set(CORE_SOURCES
    core/property_system.cpp
)

set(CORE_HEADERS
    core/types.h
    core/property_data.h
    core/property_binding.h
    core/property_observer.h
    core/property.h
    core/binding_storage.h
    modern_property.h
)

# Create the core library
add_library(modern_property_core STATIC ${CORE_SOURCES} ${CORE_HEADERS})

# Set target properties
set_target_properties(modern_property_core PROPERTIES
    CXX_STANDARD 20
    CXX_STANDARD_REQUIRED ON
    CXX_EXTENSIONS OFF
)

# Link threading library if available
find_package(Threads REQUIRED)
target_link_libraries(modern_property_core Threads::Threads)

# Examples
option(BUILD_EXAMPLES "Build example programs" ON)

if(BUILD_EXAMPLES)
    # Basic usage example
    add_executable(basic_usage examples/basic_usage.cpp)
    target_link_libraries(basic_usage modern_property_core)
    set_target_properties(basic_usage PROPERTIES
        CXX_STANDARD 20
        CXX_STANDARD_REQUIRED ON
    )

    # Advanced usage example
    add_executable(advanced_usage examples/advanced_usage.cpp)
    target_link_libraries(advanced_usage modern_property_core)
    set_target_properties(advanced_usage PROPERTIES
        CXX_STANDARD 20
        CXX_STANDARD_REQUIRED ON
    )
endif()

# Testing
option(BUILD_TESTS "Build test programs" OFF)

if(BUILD_TESTS)
    enable_testing()
    
    # Find or include a testing framework
    # For now, we'll create a simple test executable
    add_executable(property_tests tests/property_tests.cpp)
    target_link_libraries(property_tests modern_property_core)
    set_target_properties(property_tests PROPERTIES
        CXX_STANDARD 20
        CXX_STANDARD_REQUIRED ON
    )
    
    add_test(NAME PropertySystemTests COMMAND property_tests)
endif()

# Installation
option(INSTALL_MODERN_PROPERTY "Install Modern Property System" OFF)

if(INSTALL_MODERN_PROPERTY)
    include(GNUInstallDirs)
    
    # Install headers
    install(FILES ${CORE_HEADERS}
        DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}/modern_property
    )
    
    # Install library
    install(TARGETS modern_property_core
        LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
        ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
        RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
    )
    
    # Install examples if built
    if(BUILD_EXAMPLES)
        install(TARGETS basic_usage advanced_usage
            RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
        )
    endif()
    
    # Create and install config files for find_package
    include(CMakePackageConfigHelpers)
    
    write_basic_package_version_file(
        ModernPropertySystemConfigVersion.cmake
        VERSION ${PROJECT_VERSION}
        COMPATIBILITY AnyNewerVersion
    )
    
    configure_package_config_file(
        ${CMAKE_CURRENT_SOURCE_DIR}/cmake/ModernPropertySystemConfig.cmake.in
        ${CMAKE_CURRENT_BINARY_DIR}/ModernPropertySystemConfig.cmake
        INSTALL_DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/ModernPropertySystem
    )
    
    install(FILES
        ${CMAKE_CURRENT_BINARY_DIR}/ModernPropertySystemConfig.cmake
        ${CMAKE_CURRENT_BINARY_DIR}/ModernPropertySystemConfigVersion.cmake
        DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/ModernPropertySystem
    )
endif()

# Documentation
option(BUILD_DOCUMENTATION "Build documentation with Doxygen" OFF)

if(BUILD_DOCUMENTATION)
    find_package(Doxygen)
    if(DOXYGEN_FOUND)
        set(DOXYGEN_IN ${CMAKE_CURRENT_SOURCE_DIR}/docs/Doxyfile.in)
        set(DOXYGEN_OUT ${CMAKE_CURRENT_BINARY_DIR}/Doxyfile)
        
        configure_file(${DOXYGEN_IN} ${DOXYGEN_OUT} @ONLY)
        
        add_custom_target(docs ALL
            COMMAND ${DOXYGEN_EXECUTABLE} ${DOXYGEN_OUT}
            WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
            COMMENT "Generating API documentation with Doxygen"
            VERBATIM
        )
    else()
        message(WARNING "Doxygen not found, documentation will not be built")
    endif()
endif()

# Feature summary
include(FeatureSummary)

feature_summary(WHAT ALL
    DESCRIPTION "Modern Property System Build Configuration:"
)

# Print build information
message(STATUS "")
message(STATUS "Modern Property System Configuration:")
message(STATUS "  Version: ${PROJECT_VERSION}")
message(STATUS "  Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  C++ Standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  Compiler: ${CMAKE_CXX_COMPILER_ID} ${CMAKE_CXX_COMPILER_VERSION}")
message(STATUS "  Build examples: ${BUILD_EXAMPLES}")
message(STATUS "  Build tests: ${BUILD_TESTS}")
message(STATUS "  Build documentation: ${BUILD_DOCUMENTATION}")
message(STATUS "  Install: ${INSTALL_MODERN_PROPERTY}")
message(STATUS "") 