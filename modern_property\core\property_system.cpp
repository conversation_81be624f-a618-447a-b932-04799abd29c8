#include "property_data.h"
#include "property_binding.h"
#include "binding_storage.h"
#include <thread>

/**
 * @brief Modern Property System - Implementation
 * 
 * This file contains the implementation of various functions
 * declared in the header files.
 */

namespace modern_property {

// Property data implementation
void untyped_property_data::register_dependency() const {
    // Get the current binding evaluator and register this property as a dependency
    thread_local binding_evaluator* current_evaluator = nullptr;
    
    // This is a simplified implementation - in a real system, this would
    // integrate with the threading system to get the current evaluation context
    if (current_evaluator) {
        current_evaluator->register_current_dependency(this);
    }
}

// Binding storage implementation
binding_storage& global_binding_storage() {
    static binding_storage storage;
    return storage;
}

// Threading utilities implementation
namespace threading {
    
    /**
     * @brief Thread-local evaluation context
     */
    struct evaluation_context {
        binding_evaluator* current_evaluator = nullptr;
        dependency_tracker* current_tracker = nullptr;
        std::uint32_t evaluation_depth = 0;
    };
    
    /**
     * @brief Get current thread's evaluation context
     */
    evaluation_context& current_context() noexcept {
        thread_local evaluation_context context;
        return context;
    }
    
    /**
     * @brief RAII evaluation guard implementation
     */
    evaluation_guard::evaluation_guard() noexcept
        : previous_context_{&current_context()} {
        auto& context = current_context();
        ++context.evaluation_depth;
    }
    
    evaluation_guard::~evaluation_guard() noexcept {
        auto& context = current_context();
        if (context.evaluation_depth > 0) {
            --context.evaluation_depth;
        }
    }
    
} // namespace threading

} // namespace modern_property 