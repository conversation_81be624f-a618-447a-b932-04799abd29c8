#ifndef LOGGER_H
#define LOGGER_H

#include <filesystem>
#include <sstream>
#include <memory>
#include <iostream>

// https://github.com/gabime/spdlog/issues/1529
// *EDITED spdlog/include/spdlog/tweakme.h line 105

#include <spdlog/spdlog.h>
#include <spdlog/async.h>
#include <spdlog/logger.h>
#include <spdlog/fmt/fmt.h>
#include <spdlog/fmt/bundled/printf.h>
#include <spdlog/sinks/basic_file_sink.h>
#include <spdlog/sinks/daily_file_sink.h>
#include <spdlog/sinks/msvc_sink.h>
#include <spdlog/sinks/stdout_sinks.h>
#include <spdlog/sinks/stdout_color_sinks.h>

#include "platform.h"

#ifndef LOG_FUNC_SIG
#if defined(_MSC_VER)
#define LOG_FUNC_SIG __FUNCSIG__
#else
#define LOG_FUNC_SIG __PRETTY_FUNCTION__
#endif
#endif

namespace fs = std::filesystem;

//! NOTE Signature should be like
//! ReturnType modulename::maybe::ClassName::methodName()
inline std::string_view moduleNameBySig(const std::string_view& sig)
{
    static const std::string_view ArgBegin("(");
    static const std::string_view Space(" ");
    static const std::string_view Colon("::");

    std::size_t endMethod = sig.find_first_of(ArgBegin);
    if (endMethod == std::string_view::npos) {
        return sig;
    }

    std::size_t beginMethod = sig.find_last_of(Space, endMethod);
    if (beginMethod == std::string_view::npos) {
        return std::string_view();
    }

    /*size_t endModule = sig.find_first_of(Colon, beginMethod);
    if (endModule == std::string_view::npos) {
        return std::string_view();
    }*/

    std::string_view module = sig.substr(beginMethod, endMethod);
    return module;
}

class Logger
{
public:
    struct LogStream : public std::ostringstream
    {
    public:
        LogStream(const spdlog::source_loc& _loc, spdlog::level::level_enum _lvl, std::string_view _prefix)
            : loc(_loc)
            , lvl(_lvl)
            , prefix(_prefix)
        {
        }

        ~LogStream()
        {
            flush();
        }

        void flush()
        {
            std::cout << "LOG: " << (prefix + str()).c_str() << std::endl;
            Logger::get().log(loc, lvl, (prefix + str()).c_str());
        }

    private:
        spdlog::source_loc loc;
        spdlog::level::level_enum lvl = spdlog::level::info;
        std::string prefix;
    };

public:
    static Logger& get()
    {
        static Logger logger;
        logger.init(LOG_DIR + "log.log");
        return logger;
    }

    bool init(std::string_view log_file_path)
    {
        if (m_inited) {
            return true;
        }
        try {
            // check log path and try to create log directory
            fs::path log_path(log_file_path);
            fs::path log_dir = log_path.parent_path();
            if (!fs::exists(log_path)) {
                fs::create_directories(log_dir);
            }
            // initialize spdlog
            constexpr std::size_t log_buffer_size = 32 * 1024; // 32kb
            // constexpr std::size_t max_file_size = 50 * 1024 * 1024; // 50mb
            spdlog::init_thread_pool(log_buffer_size, std::thread::hardware_concurrency());
            std::vector<spdlog::sink_ptr> sinks;
            auto daily_sink = std::make_shared<spdlog::sinks::daily_file_sink_mt>(log_path.string(), 0, 2);
            sinks.push_back(daily_sink);

            // auto file_sink = std::make_shared<spdlog::sinks::basic_file_sink_mt>(log_path.string(), true);
            // sinks.push_back(file_sink);

#if defined(_DEBUG) && defined(WIN32) && !defined(NO_CONSOLE_LOG)
            auto ms_sink = std::make_shared<spdlog::sinks::msvc_sink_mt>();
            sinks.push_back(ms_sink);
#endif //  _DEBUG

#if !defined(WIN32) && !defined(NO_CONSOLE_LOG)
            auto console_sink = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
            sinks.push_back(console_sink);
#endif
            spdlog::set_default_logger(std::make_shared<spdlog::logger>("", sinks.begin(), sinks.end()));

            spdlog::set_pattern("[%Y-%m-%d %H:%M:%S.%e] [%l] [%P] [%t] %!: %v");
            spdlog::flush_on(spdlog::level::info);
            spdlog::set_level(m_level);
        }
        catch (std::exception_ptr e)
        {
            assert(false);
            return false;
        }
        m_inited = true;
        return true;
    }

    void shutdown()
    {
        spdlog::shutdown();
    }

    template<typename ... Args>
    void log(const spdlog::source_loc& loc, spdlog::level::level_enum lvl, const char* fmt, const Args&... args)
    {
        spdlog::log(loc, lvl, fmt, args ...);
    }

    template<typename ... Args>
    void printf(const spdlog::source_loc& loc, spdlog::level::level_enum lvl, const char* fmt, const Args&... args)
    {
        spdlog::log(loc, lvl, fmt::sprintf(fmt, args ...).c_str());
    }

    spdlog::level::level_enum level()
    {
        return m_level;
    }

    void set_level(spdlog::level::level_enum lvl)
    {
        m_level = lvl;
        spdlog::set_level(lvl);
    }

    void set_flush_on(spdlog::level::level_enum lvl)
    {
        spdlog::flush_on(lvl);
    }

    static const char* get_shortname(std::string_view path)
    {
        if (path.empty()) {
            return path.data();
        }

        size_t pos = path.find_last_of("/\\");
        return path.data() + ((pos == path.npos) ? 0 : pos + 1);
    }

private:
    Logger() = default;
    ~Logger()
    {
        //std::cout << "Logger::Destruction" << std::endl;
        //spdlog::shutdown();
    }

    Logger(const Logger&) = delete;
    void operator=(const Logger&) = delete;

private:
    std::atomic_bool m_inited = false;
    spdlog::level::level_enum m_level = spdlog::level::trace;
};

/*class logger_none {
public:
    logger_none() = default;

    static logger_none& get() {
        static logger_none logger;
        return logger;
    }

    logger_none& operator<<(const char* content) {
        return *this;
    }
};*/

#endif // LOGGER_H
