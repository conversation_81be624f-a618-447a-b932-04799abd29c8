#ifndef MODULARITY_MODULEINFO_H
#define MODULARITY_MODULEINFO_H

#include <string_view>
#include <iostream>

#ifndef FUNC_SIG
#if defined(_MSC_VER)
#define FUNC_SIG __FUNCSIG__
#else
#define FUNC_SIG __PRETTY_FUNCTION__
#endif
#endif

#if defined(__GNUC__) && !defined(__clang__)
#if (__GNUC__ < 11)
#define IOC_NO_STRINGVIEW_CONSTEXPR_METHODS
#endif
#endif

#define FUNCNAME modularity::funcNameBySig(FUNC_SIG)
#define CLASSNAME modularity::classNameBySig(FUNC_SIG)
#define CLASSFUNC modularity::classFuncBySig(FUNC_SIG)
#define MODULENAME modularity::moduleNameBySig(FUNC_SIG)

namespace modularity {
struct InterfaceInfo {
    std::string_view id;
    std::string_view module;
    bool internal = false;
    constexpr InterfaceInfo(std::string_view i, std::string_view m, bool intr)
        : id(i), module(m), internal(intr) {}
};

//! NOTE Signature should be like
//! ReturnType modulename::maybe::ClassName::methodName()
inline std::string_view moduleNameBySig(const std::string_view& sig)
{
    static const std::string_view ArgBegin("(");
    static const std::string_view Space(" ");
    static const std::string_view Colon("::");

    std::size_t endMethod = sig.find_first_of(ArgBegin);
    if (endMethod == std::string_view::npos) {
        return sig;
    }

    std::size_t beginMethod = sig.find_last_of(Space, endMethod);
    if (beginMethod == std::string_view::npos) {
        return std::string_view();
    }

    size_t endModule = sig.find_first_of(Colon, beginMethod);
    if (endModule == std::string_view::npos) {
        return std::string_view();
    }

    std::string_view module = sig.substr(beginMethod, endModule - beginMethod);
    //std::cout << "ModuleInfo::moduleNameBySig:" << module << std::endl;
    return module;
}
}

#endif // MODULARITY_MODULEINFO_H
