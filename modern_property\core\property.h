#pragma once

#include "types.h"
#include "property_data.h"
#include "property_binding.h"
#include "property_observer.h"
#include <memory>
#include <optional>

/**
 * @brief Modern Property System - Main Property Class
 * 
 * This file contains the main Property class that integrates all system components
 * and provides a user-friendly API compatible with the original system.
 */

namespace modern_property {

// Forward declarations
class binding_storage;
namespace threading { class evaluation_context; }

/**
 * @brief Main property class with modern C++20 features
 * @tparam T The type of value stored in this property
 * 
 * This class provides the main user interface for the property system,
 * integrating data storage, bindings, and observers into a cohesive API.
 */
template<Property_storable T>
class property : public property_data<T> {
public:
    /// @brief Base type
    using base_type = property_data<T>;
    
    /// @brief Value type stored by this property
    using value_type = T;
    
    /// @brief Parameter types for efficient passing
    using const_param_type = typename parameter_traits<T>::const_param_type;
    using param_type = typename parameter_traits<T>::param_type;
    using rvalue_type = typename parameter_traits<T>::rvalue_type;
    
    /// @brief Binding type for this property
    using binding_type = property_binding<T>;
    
    /// @brief Observer type for this property
    using observer_type = typed_property_observer<T>;

    /**
     * @brief Default constructor - value-initializes the stored value
     */
    property() requires std::is_default_constructible_v<T>
        : base_type{}, observers_{std::make_unique<property_observer_list>()} {}
    
    /**
     * @brief Constructor with initial value (copy)
     * @param initial_value The initial value to store
     */
    explicit property(const_param_type initial_value) 
        requires std::is_copy_constructible_v<T>
        : base_type{initial_value}, observers_{std::make_unique<property_observer_list>()} {}
    
    /**
     * @brief Constructor with initial value (move)
     * @param initial_value The initial value to store (moved)
     */
    explicit property(rvalue_type initial_value) 
        requires std::is_move_constructible_v<T>
        : base_type{std::move(initial_value)}, observers_{std::make_unique<property_observer_list>()} {}
        
    /**
     * @brief Constructor with initial binding
     * @param binding Initial binding for this property
     */
    explicit property(const binding_type& binding)
        : property{} {
        set_binding(binding);
    }
    
    /**
     * @brief Constructor with initial binding (move)
     * @param binding Initial binding for this property (moved)
     */
    explicit property(binding_type&& binding)
        : property{} {
        set_binding(std::move(binding));
    }
    
    /// @brief Move constructor
    property(property&& other) noexcept 
        : base_type{std::move(other)}, 
          binding_{std::move(other.binding_)},
          observers_{std::move(other.observers_)},
          dependency_tracker_{std::move(other.dependency_tracker_)} {}
    
    /// @brief Move assignment
    property& operator=(property&& other) noexcept {
        if (this != &other) {
            base_type::operator=(std::move(other));
            binding_ = std::move(other.binding_);
            observers_ = std::move(other.observers_);
            dependency_tracker_ = std::move(other.dependency_tracker_);
        }
        return *this;
    }
    
    /// @brief Destructor
    ~property() override = default;
    
    MODERN_PROPERTY_DISABLE_COPY(property)

    // Value access methods (compatible with original API)
    
    /**
     * @brief Get the current value
     * @return Current value of the property
     */
    [[nodiscard]] const_param_type value() const {
        // Check if we need to evaluate binding first
        if (binding_ && should_reevaluate_binding()) {
            const_cast<property*>(this)->evaluate_binding();
        }
        
        // Use base class method which handles dependency registration
        return base_type::value();
    }
    
    /**
     * @brief Set new value (copy version)
     * @param new_value The new value to store
     */
    void setValue(const_param_type new_value) 
        requires std::is_copy_assignable_v<T> {
        set_value_internal(new_value);
    }
    
    /**
     * @brief Set new value (move version)  
     * @param new_value The new value to store (moved)
     */
    void setValue(rvalue_type new_value) 
        requires std::is_move_assignable_v<T> {
        set_value_internal(std::move(new_value));
    }
    
    /**
     * @brief Assignment operator (copy)
     * @param new_value New value to assign
     */
    property& operator=(const_param_type new_value) 
        requires std::is_copy_assignable_v<T> {
        setValue(new_value);
        return *this;
    }
    
    /**
     * @brief Assignment operator (move)
     * @param new_value New value to assign (moved)
     */
    property& operator=(rvalue_type new_value) 
        requires std::is_move_assignable_v<T> {
        setValue(std::move(new_value));
        return *this;
    }
    
    /**
     * @brief Implicit conversion to value type
     * @return Current value of the property
     */
    [[nodiscard]] operator const_param_type() const {
        return value();
    }

    // Binding management methods (compatible with original API)
    
    /**
     * @brief Set a binding for this property
     * @param binding New binding to set
     * @return Previous binding (if any)
     */
    std::optional<binding_type> setBinding(const binding_type& binding) {
        return set_binding_internal(binding);
    }
    
    /**
     * @brief Set a binding for this property (move version)
     * @param binding New binding to set (moved)
     * @return Previous binding (if any)
     */
    std::optional<binding_type> setBinding(binding_type&& binding) {
        return set_binding_internal(std::move(binding));
    }
    
    /**
     * @brief Set a binding using a lambda or function object
     * @tparam F Function type (deduced)
     * @param func Function that computes the binding value
     * @param location Source location for debugging
     * @return Previous binding (if any)
     */
    template<typename F>
    std::optional<binding_type> setBinding(F&& func, 
                                         const source_location_type& location = source_location_type::current())
        requires Property_binding_function<F, T> {
        return setBinding(binding_type{std::forward<F>(func), location});
    }
    
    /**
     * @brief Check if this property has a binding
     * @return true if bound, false otherwise
     */
    [[nodiscard]] bool hasBinding() const noexcept override {
        return binding_.has_value();
    }
    
    /**
     * @brief Remove the current binding
     * @return The removed binding (if any)
     */
    std::optional<binding_type> removeBinding() {
        std::optional<binding_type> old_binding;
        if (binding_) {
            old_binding = std::move(*binding_);
            binding_.reset();
            clear_dependencies();
        }
        return old_binding;
    }
    
    /**
     * @brief Get the current binding
     * @return Current binding (if any)
     */
    [[nodiscard]] const std::optional<binding_type>& binding() const noexcept {
        return binding_;
    }

    // Observer management methods (compatible with original API)
    
    /**
     * @brief Add an observer for value changes
     * @param handler Function to call when value changes
     * @param priority Observer priority level
     * @return Reference to the added observer
     */
    property_observer& onValueChanged(property_observer::change_handler handler,
                                    observer_priority priority = observer_priority::normal) {
        return observers_->add_observer(std::move(handler), priority);
    }
    
    /**
     * @brief Add a typed observer for value changes with old/new value access
     * @param handler Function to call with old and new values
     * @param priority Observer priority level
     * @return Reference to the added typed observer
     */
    observer_type& onValueChanged(typename observer_type::typed_change_handler handler,
                                observer_priority priority = observer_priority::normal) {
        auto observer = observer_factory::create_value_observer<T>(std::move(handler), priority);
        auto& ref = *observer;
        observers_->add_observer(std::move(observer));
        return ref;
    }
    
    /**
     * @brief Remove an observer by ID
     * @param observer_id Unique ID of observer to remove
     * @return true if observer was found and removed
     */
    bool removeObserver(const void* observer_id) {
        return observers_->remove_observer(observer_id);
    }
    
    /**
     * @brief Remove all observers
     */
    void clearObservers() {
        observers_->clear();
    }
    
    /**
     * @brief Get number of observers
     * @return Observer count
     */
    [[nodiscard]] size_type observerCount() const noexcept {
        return observers_->size();
    }

    // Advanced methods
    
    /**
     * @brief Force re-evaluation of binding (if present)
     * @return true if value changed as a result
     */
    bool reevaluateBinding() {
        if (!binding_) {
            return false;
        }
        return evaluate_binding();
    }
    
    /**
     * @brief Get list of properties this property depends on
     * @return List of dependencies
     */
    [[nodiscard]] const auto& dependencies() const noexcept {
        return dependency_tracker_.dependencies();
    }
    
    /**
     * @brief Check if this property depends on another property
     * @param other_property Property to check
     * @return true if dependent
     */
    [[nodiscard]] bool dependsOn(const untyped_property_data* other_property) const noexcept {
        const auto& deps = dependencies();
        return std::find(deps.begin(), deps.end(), other_property) != deps.end();
    }

protected:
    /**
     * @brief Notify observers (override from base class)
     */
    void notify_observers() const override {
        if (observers_ && !observers_->empty()) {
            get_notification_manager().notify(this, observers_.get());
        }
    }

private:
    /**
     * @brief Internal value setting with change detection and notifications
     * @tparam U Value type (deduced)
     * @param new_value New value to set
     */
    template<typename U>
    void set_value_internal(U&& new_value) {
        // Clear binding when value is set manually
        if (binding_) {
            binding_.reset();
            clear_dependencies();
        }
        
        // Store old value for observers
        std::optional<T> old_value;
        if constexpr (std::is_copy_constructible_v<T>) {
            old_value = base_type::value_bypassing_bindings();
        }
        
        // Set new value and check if it changed
        bool changed = base_type::set_value(std::forward<U>(new_value));
        
        // Notify typed observers with old/new values if value changed
        if (changed && old_value && observers_) {
            notify_typed_observers(*old_value, base_type::value_bypassing_bindings());
        }
    }
    
    /**
     * @brief Internal binding setting logic
     * @tparam U Binding type (deduced)
     * @param new_binding New binding to set
     * @return Previous binding (if any)
     */
    template<typename U>
    std::optional<binding_type> set_binding_internal(U&& new_binding) {
        std::optional<binding_type> old_binding;
        if (binding_) {
            old_binding = std::move(*binding_);
        }
        
        binding_ = std::forward<U>(new_binding);
        
        // Immediately evaluate the new binding
        evaluate_binding();
        
        return old_binding;
    }
    
    /**
     * @brief Evaluate the current binding and update value
     * @return true if value changed
     */
    bool evaluate_binding() {
        if (!binding_) {
            return false;
        }
        
        // Use global binding evaluator for circular dependency detection
        auto& evaluator = get_binding_evaluator();
        auto error = evaluator.evaluate_binding(*binding_, this, dependency_tracker_);
        
        if (error.has_error()) {
            // Handle binding error (could log, throw, or ignore based on policy)
            handle_binding_error(error);
            return false;
        }
        
        return true;
    }
    
    /**
     * @brief Check if binding should be re-evaluated
     * @return true if re-evaluation needed
     */
    bool should_reevaluate_binding() const {
        // For now, always re-evaluate when accessed
        // In a more sophisticated implementation, this could track
        // dependency changes to avoid unnecessary evaluations
        return true;
    }
    
    /**
     * @brief Clear dependency tracking
     */
    void clear_dependencies() {
        dependency_tracker_.clear();
    }
    
    /**
     * @brief Notify typed observers with old and new values
     * @param old_value Previous value
     * @param new_value Current value
     */
    void notify_typed_observers(const T& old_value, const T& new_value) const {
        // This would require changes to observer_list to support typed observers
        // For now, we'll use the regular notification mechanism
        (void)old_value;
        (void)new_value;
    }
    
    /**
     * @brief Handle binding evaluation errors
     * @param error Error information
     */
    void handle_binding_error(const binding_error& error) const {
        // In a real implementation, this could:
        // - Log the error
        // - Throw an exception
        // - Call error handlers
        // - Disable the binding
        (void)error; // For now, ignore errors
    }
    
    /**
     * @brief Get thread-local binding evaluator
     * @return Reference to binding evaluator
     */
    static binding_evaluator& get_binding_evaluator() {
        thread_local binding_evaluator evaluator;
        return evaluator;
    }
    
    /**
     * @brief Get thread-local notification manager
     * @return Reference to notification manager
     */
    static notification_manager& get_notification_manager() {
        thread_local notification_manager manager;
        return manager;
    }

    // Member variables
    std::optional<binding_type> binding_;
    std::unique_ptr<property_observer_list> observers_;
    mutable dependency_tracker dependency_tracker_;
};

/**
 * @brief Type trait to check if a type is a property
 * @tparam T Type to check
 */
template<typename T>
struct is_property : std::false_type {};

template<Property_storable T>
struct is_property<property<T>> : std::true_type {};

/// @brief Convenience variable template for is_property
template<typename T>
constexpr bool is_property_v = is_property<T>::value;

/**
 * @brief Factory functions for creating properties
 */
namespace property_factory {
    /**
     * @brief Create a property with initial value
     * @tparam T Property type (deduced)
     * @param initial_value Initial value
     * @return New property with given value
     */
    template<Property_storable T>
    property<T> create_with_value(const T& initial_value) {
        return property<T>{initial_value};
    }
    
    /**
     * @brief Create a property with initial binding
     * @tparam F Function type (deduced)
     * @param func Binding function
     * @param location Source location for debugging
     * @return New property with given binding
     */
    template<typename F>
    auto create_with_binding(F&& func, 
                           const source_location_type& location = source_location_type::current()) {
        using return_type = std::invoke_result_t<F>;
        property<return_type> prop;
        prop.setBinding(std::forward<F>(func), location);
        return prop;
    }
}

} // namespace modern_property 