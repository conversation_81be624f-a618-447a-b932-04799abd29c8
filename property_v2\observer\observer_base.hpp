#pragma once

/**
 * @file observer_base.hpp
 * @brief 属性观察者系统的基础设施
 * 
 * 定义观察者模式的核心接口和基础类型。
 * 
 * <AUTHOR> System V2
 * @date 2025
 */

#include <functional>
#include <memory>
#include <atomic>
#include "../core/concepts.hpp"
#include "../core/utils.hpp"
#include "../binding/binding_base.hpp"

namespace property_v2::observer {

// 前向声明
class property_observer;
class observer_list;
namespace binding { class binding_data; }

/**
 * @brief 观察者类型枚举
 */
enum class observer_type {
    change_handler,     ///< 变更处理器
    binding_notifier,   ///< 绑定通知器
    placeholder         ///< 占位符（用于遍历保护）
};

/**
 * @brief 观察者基类
 * 
 * 所有属性观察者的基类，提供基本的链表节点功能。
 */
class observer_base {
public:
    /**
     * @brief 默认构造函数
     */
    observer_base() noexcept = default;
    
    /**
     * @brief 虚析构函数
     */
    virtual ~observer_base() = default;
    
    /**
     * @brief 禁用拷贝
     */
    PROPERTY_DISABLE_COPY(observer_base)
    
    /**
     * @brief 移动构造函数
     */
    observer_base(observer_base&& other) noexcept;
    
    /**
     * @brief 移动赋值运算符
     */
    observer_base& operator=(observer_base&& other) noexcept;
    
    /**
     * @brief 获取观察者类型
     */
    virtual observer_type get_type() const noexcept = 0;
    
    /**
     * @brief 通知观察者
     * @param property_data 属性数据指针
     */
    virtual void notify(binding::untyped_property_data* property_data) = 0;
    
    /**
     * @brief 获取下一个观察者
     */
    property_observer* get_next() const noexcept { return next_; }
    
    /**
     * @brief 设置下一个观察者
     */
    void set_next(property_observer* next) noexcept { next_ = next; }
    
    /**
     * @brief 获取前一个观察者指针的指针
     */
    property_observer** get_prev() const noexcept { return prev_; }
    
    /**
     * @brief 设置前一个观察者指针的指针
     */
    void set_prev(property_observer** prev) noexcept { prev_ = prev; }

private:
    property_observer* next_ = nullptr;      ///< 下一个观察者
    property_observer** prev_ = nullptr;     ///< 前一个观察者指针的指针
    
    friend class observer_list;
    friend class property_observer;
};

/**
 * @brief 属性观察者类
 * 
 * 具体的属性观察者实现，支持不同类型的观察者。
 */
class property_observer : public observer_base {
public:
    using change_handler_fn = std::function<void()>;
    
    /**
     * @brief 默认构造函数
     */
    property_observer() noexcept = default;
    
    /**
     * @brief 移动构造函数
     */
    property_observer(property_observer&& other) noexcept;
    
    /**
     * @brief 移动赋值运算符
     */
    property_observer& operator=(property_observer&& other) noexcept;
    
    /**
     * @brief 析构函数
     */
    ~property_observer() override;
    
    /**
     * @brief 设置为变更处理器
     * @param handler 处理函数
     */
    void set_change_handler(change_handler_fn handler) noexcept;
    
    /**
     * @brief 设置为绑定通知器
     * @param binding 绑定对象
     */
    void set_binding_notifier(binding::binding_private* binding) noexcept;
    
    /**
     * @brief 获取观察者类型
     */
    observer_type get_type() const noexcept override { return type_; }
    
    /**
     * @brief 通知观察者
     */
    void notify(binding::untyped_property_data* property_data) override;
    
    /**
     * @brief 从观察者列表中移除
     */
    void unlink() noexcept;
    
    /**
     * @brief 设置观察的属性
     * @param binding_data 绑定数据
     */
    void set_source(binding::binding_data* binding_data);

private:
    observer_type type_ = observer_type::change_handler;
    
    union {
        change_handler_fn change_handler_;          ///< 变更处理函数
        binding::binding_private* binding_;         ///< 绑定对象指针
    };
    
    /**
     * @brief 清理联合体数据
     */
    void cleanup_union_data() noexcept;
};

/**
 * @brief 观察者列表类
 * 
 * 管理属性的观察者链表。
 */
class observer_list {
public:
    /**
     * @brief 默认构造函数
     */
    observer_list() noexcept = default;
    
    /**
     * @brief 析构函数
     */
    ~observer_list();
    
    /**
     * @brief 禁用拷贝和移动
     */
    PROPERTY_DISABLE_COPY_MOVE(observer_list)
    
    /**
     * @brief 添加观察者
     * @param observer 观察者指针
     */
    void add_observer(property_observer* observer) noexcept;
    
    /**
     * @brief 移除观察者
     * @param observer 观察者指针
     */
    void remove_observer(property_observer* observer) noexcept;
    
    /**
     * @brief 通知所有观察者
     * @param property_data 属性数据指针
     */
    void notify_all(binding::untyped_property_data* property_data);
    
    /**
     * @brief 获取观察者数量
     */
    std::size_t size() const noexcept;
    
    /**
     * @brief 检查是否为空
     */
    bool empty() const noexcept { return first_observer_ == nullptr; }
    
    /**
     * @brief 获取第一个观察者
     */
    property_observer* get_first() const noexcept { return first_observer_; }
    
    /**
     * @brief 清除所有观察者
     */
    void clear() noexcept;

private:
    property_observer* first_observer_ = nullptr;  ///< 第一个观察者
    
    /**
     * @brief 观察者节点保护器
     * 
     * 在遍历观察者列表时保护当前节点不被删除。
     */
    class observer_node_protector {
    public:
        explicit observer_node_protector(property_observer* observer) noexcept;
        ~observer_node_protector() noexcept;
        
        PROPERTY_DISABLE_COPY_MOVE(observer_node_protector)
        
        property_observer* get_next() const noexcept { return next_; }

    private:
        observer_base placeholder_;
        property_observer* next_;
    };
};

/**
 * @brief 观察者智能指针
 * 
 * 自动管理观察者的生命周期。
 */
class observer_ptr {
public:
    /**
     * @brief 默认构造函数
     */
    observer_ptr() noexcept = default;
    
    /**
     * @brief 从原始指针构造
     */
    explicit observer_ptr(property_observer* observer) noexcept : observer_(observer) {}
    
    /**
     * @brief 移动构造函数
     */
    observer_ptr(observer_ptr&& other) noexcept 
        : observer_(std::exchange(other.observer_, nullptr)) {}
    
    /**
     * @brief 禁用拷贝
     */
    PROPERTY_DISABLE_COPY(observer_ptr)
    
    /**
     * @brief 移动赋值运算符
     */
    observer_ptr& operator=(observer_ptr&& other) noexcept {
        if (this != &other) {
            reset();
            observer_ = std::exchange(other.observer_, nullptr);
        }
        return *this;
    }
    
    /**
     * @brief 析构函数
     */
    ~observer_ptr() { reset(); }
    
    /**
     * @brief 重置指针
     */
    void reset(property_observer* observer = nullptr) noexcept {
        if (observer_) {
            observer_->unlink();
            delete observer_;
        }
        observer_ = observer;
    }
    
    /**
     * @brief 释放指针所有权
     */
    property_observer* release() noexcept {
        return std::exchange(observer_, nullptr);
    }
    
    /**
     * @brief 获取原始指针
     */
    property_observer* get() const noexcept { return observer_; }
    
    /**
     * @brief 解引用运算符
     */
    property_observer& operator*() const noexcept { return *observer_; }
    
    /**
     * @brief 箭头运算符
     */
    property_observer* operator->() const noexcept { return observer_; }
    
    /**
     * @brief 布尔转换运算符
     */
    explicit operator bool() const noexcept { return observer_ != nullptr; }

private:
    property_observer* observer_ = nullptr;
};

} // namespace property_v2::observer
