#include "folder.h"
#include <filesystem>
#include <algorithm>
#include <regex>

namespace fs = std::filesystem;
namespace filesystem {

Folder::Folder() = default;

Folder::Folder(const std::string& path) : m_path(path) {
    // Normalize path separators to forward slashes
    std::replace(m_path.begin(), m_path.end(), '\\', '/');
}

Folder::Folder(const Path& path) : m_path(path.toString()) {}

Folder::Folder(const Folder& other) = default;

Folder::Folder(Folder&& other) noexcept = default;

Folder& Folder::operator=(const Folder& other) = default;

Folder& Folder::operator=(Folder&& other) noexcept = default;

std::string Folder::path() const {
    return m_path;
}

void Folder::setPath(const std::string& path) {
    m_path = path;
    // Normalize path separators to forward slashes
    std::replace(m_path.begin(), m_path.end(), '\\', '/');
}

void Folder::setPath(const Path& path) {
    m_path = path.toString();
}

bool Folder::exists() const {
    if (m_path.empty()) {
        return false;
    }
    
    try {
        return fs::exists(m_path) && fs::is_directory(m_path);
    } catch (const fs::filesystem_error& e) {
        m_errorString = e.what();
        return false;
    }
}

bool Folder::create(bool createParents) {
    if (m_path.empty()) {
        m_errorString = "Empty path";
        return false;
    }
    
    try {
        if (exists()) {
            return true;
        }
        
        if (createParents) {
            return fs::create_directories(m_path);
        } else {
            return fs::create_directory(m_path);
        }
    } catch (const fs::filesystem_error& e) {
        m_errorString = e.what();
        return false;
    }
}

bool Folder::remove(bool recursive) {
    if (m_path.empty()) {
        m_errorString = "Empty path";
        return false;
    }
    
    try {
        if (!exists()) {
            return true;
        }
        
        if (recursive) {
            return fs::remove_all(m_path) > 0;
        } else {
            return fs::remove(m_path);
        }
    } catch (const fs::filesystem_error& e) {
        m_errorString = e.what();
        return false;
    }
}

bool Folder::rename(const std::string& newName) {
    if (m_path.empty()) {
        m_errorString = "Empty path";
        return false;
    }
    
    try {
        if (!exists()) {
            m_errorString = "Directory does not exist";
            return false;
        }
        
        fs::rename(m_path, newName);
        m_path = newName;
        return true;
    } catch (const fs::filesystem_error& e) {
        m_errorString = e.what();
        return false;
    }
}

Folder Folder::parentFolder() const {
    if (m_path.empty()) {
        return Folder();
    }
    
    try {
        fs::path path(m_path);
        return Folder(path.parent_path().string());
    } catch (const fs::filesystem_error& e) {
        m_errorString = e.what();
        return Folder();
    }
}

std::string Folder::absolutePath() const {
    if (m_path.empty()) {
        return "";
    }
    
    try {
        return fs::absolute(m_path).string();
    } catch (const fs::filesystem_error& e) {
        m_errorString = e.what();
        return "";
    }
}

std::string Folder::canonicalPath() const {
    if (m_path.empty() || !exists()) {
        return "";
    }
    
    try {
        return fs::canonical(m_path).string();
    } catch (const fs::filesystem_error& e) {
        m_errorString = e.what();
        return "";
    }
}

bool Folder::isEmpty() const {
    if (m_path.empty() || !exists()) {
        return true;
    }
    
    try {
        return fs::is_empty(m_path);
    } catch (const fs::filesystem_error& e) {
        m_errorString = e.what();
        return true;
    }
}

std::vector<FileInfo> Folder::entryList(
    const std::vector<std::string>& nameFilters,
    bool includeFiles,
    bool includeDirs,
    bool includeHidden) const {
    std::vector<FileInfo> result;
    
    if (m_path.empty() || !exists()) {
        return result;
    }
    
    try {
        for (const auto& entry : fs::directory_iterator(m_path)) {
            std::string name = entry.path().filename().string();
            
            // Skip if it doesn't match the filter
            if (!nameFilters.empty() && !matchesFilter(name, nameFilters)) {
                continue;
            }
            
            FileInfo fileInfo(entry);
            
            // Skip if it's a file and we don't want files
            if (fileInfo.isFile() && !includeFiles) {
                continue;
            }
            
            // Skip if it's a directory and we don't want directories
            if (fileInfo.isDir() && !includeDirs) {
                continue;
            }
            
            // Skip if it's hidden and we don't want hidden entries
            if (fileInfo.isHidden() && !includeHidden) {
                continue;
            }
            
            result.push_back(fileInfo);
        }
    } catch (const fs::filesystem_error& e) {
        m_errorString = e.what();
    }
    
    return result;
}

std::vector<std::string> Folder::entryNameList(
    const std::vector<std::string>& nameFilters,
    bool includeFiles,
    bool includeDirs,
    bool includeHidden) const {
    std::vector<std::string> result;
    
    if (m_path.empty() || !exists()) {
        return result;
    }
    
    try {
        for (const auto& entry : fs::directory_iterator(m_path)) {
            std::string name = entry.path().filename().string();
            
            // Skip if it doesn't match the filter
            if (!nameFilters.empty() && !matchesFilter(name, nameFilters)) {
                continue;
            }
            
            FileInfo fileInfo(entry);
            
            // Skip if it's a file and we don't want files
            if (fileInfo.isFile() && !includeFiles) {
                continue;
            }
            
            // Skip if it's a directory and we don't want directories
            if (fileInfo.isDir() && !includeDirs) {
                continue;
            }
            
            // Skip if it's hidden and we don't want hidden entries
            if (fileInfo.isHidden() && !includeHidden) {
                continue;
            }
            
            result.push_back(name);
        }
    } catch (const fs::filesystem_error& e) {
        m_errorString = e.what();
    }
    
    return result;
}

Folder Folder::current() {
    try {
        return Folder(fs::current_path().string());
    } catch (const fs::filesystem_error&) {
        return Folder();
    }
}

bool Folder::setCurrent(const std::string& path) {
    try {
        fs::current_path(path);
        return true;
    } catch (const fs::filesystem_error&) {
        return false;
    }
}

Folder Folder::home() {
#ifdef _WIN32
    const char* homeDrive = std::getenv("HOMEDRIVE");
    const char* homePath = std::getenv("HOMEPATH");
    if (homeDrive && homePath) {
        return Folder(std::string(homeDrive) + std::string(homePath));
    }
    return Folder(std::getenv("USERPROFILE") ? std::getenv("USERPROFILE") : "");
#else
    return Folder(std::getenv("HOME") ? std::getenv("HOME") : "");
#endif
}

Folder Folder::temp() {
    try {
        return Folder(fs::temp_directory_path().string());
    } catch (const fs::filesystem_error&) {
        return Folder();
    }
}

Folder Folder::root() {
#ifdef _WIN32
    return Folder("C:/");
#else
    return Folder("/");
#endif
}

bool Folder::matchesFilter(const std::string& name, const std::vector<std::string>& filters) const {
    for (const auto& filter : filters) {
        // Convert glob pattern to regex
        std::string regexPattern = filter;
        // Replace . with \. (escape dot)
        size_t pos = 0;
        while ((pos = regexPattern.find(".", pos)) != std::string::npos) {
            regexPattern.replace(pos, 1, "\\.");
            pos += 2;
        }
        // Replace * with .*
        pos = 0;
        while ((pos = regexPattern.find("*", pos)) != std::string::npos) {
            regexPattern.replace(pos, 1, ".*");
            pos += 2;
        }
        // Replace ? with .
        pos = 0;
        while ((pos = regexPattern.find("?", pos)) != std::string::npos) {
            regexPattern.replace(pos, 1, ".");
            pos += 1;
        }
        
        std::regex regex(regexPattern, std::regex_constants::icase);
        if (std::regex_match(name, regex)) {
            return true;
        }
    }
    
    return false;
}

} // namespace filesystem
