﻿#include "filesystem.h"
#include <filesystem>

namespace fs = std::filesystem;
namespace filesystem {

FileSystem::FileSystem() : m_fileInfoGatherer(std::make_unique<FileInfoGatherer>()) {
}

FileSystem::~FileSystem() = default;

bool FileSystem::createFile(const std::string& path, bool overwrite) {
    try {
        // Check if the file already exists
        if (fs::exists(path) && !overwrite) {
            m_errorString = "File already exists";
            return false;
        }

        // Create the file with WriteOnly and Truncate modes
        // Use static_cast to combine enum values to avoid binary operator issues
        File::OpenMode mode = static_cast<File::OpenMode>(
            static_cast<int>(File::OpenMode::WriteOnly) |
            static_cast<int>(File::OpenMode::Truncate)
        );
        std::shared_ptr<File> file = openFile(path, mode);
        if (!file) {
            return false;
        }

        // Close the file
        file->close();
        return true;
    } catch (const std::exception& e) {
        m_errorString = e.what();
        return false;
    }
}

bool FileSystem::createDirectory(const std::string& path, bool createParents) {
    try {
        if (createParents) {
            return fs::create_directories(path);
        } else {
            return fs::create_directory(path);
        }
    } catch (const std::exception& e) {
        m_errorString = e.what();
        return false;
    }
}

bool FileSystem::removeFile(const std::string& path) {
    try {
        if (!fs::exists(path)) {
            m_errorString = "File does not exist";
            return false;
        }

        if (!fs::is_regular_file(path)) {
            m_errorString = "Path is not a file";
            return false;
        }

        return fs::remove(path);
    } catch (const std::exception& e) {
        m_errorString = e.what();
        return false;
    }
}

bool FileSystem::removeDirectory(const std::string& path, bool recursive) {
    try {
        if (!fs::exists(path)) {
            m_errorString = "Directory does not exist";
            return false;
        }

        if (!fs::is_directory(path)) {
            m_errorString = "Path is not a directory";
            return false;
        }

        if (recursive) {
            return fs::remove_all(path) > 0;
        } else {
            return fs::remove(path);
        }
    } catch (const std::exception& e) {
        m_errorString = e.what();
        return false;
    }
}

bool FileSystem::copyFile(const std::string& source, const std::string& destination, bool overwrite) {
    try {
        if (!fs::exists(source)) {
            m_errorString = "Source file does not exist";
            return false;
        }

        if (!fs::is_regular_file(source)) {
            m_errorString = "Source path is not a file";
            return false;
        }

        if (fs::exists(destination) && !overwrite) {
            m_errorString = "Destination file already exists";
            return false;
        }

        fs::copy_file(source, destination, overwrite ? fs::copy_options::overwrite_existing : fs::copy_options::none);
        return true;
    } catch (const std::exception& e) {
        m_errorString = e.what();
        return false;
    }
}

bool FileSystem::moveFile(const std::string& source, const std::string& destination, bool overwrite) {
    try {
        if (!fs::exists(source)) {
            m_errorString = "Source file does not exist";
            return false;
        }

        if (!fs::is_regular_file(source)) {
            m_errorString = "Source path is not a file";
            return false;
        }

        if (fs::exists(destination)) {
            if (!overwrite) {
                m_errorString = "Destination file already exists";
                return false;
            }
            fs::remove(destination);
        }

        fs::rename(source, destination);
        return true;
    } catch (const std::exception& e) {
        m_errorString = e.what();
        return false;
    }
}

bool FileSystem::copyDirectory(const std::string& source, const std::string& destination, bool recursive) {
    try {
        if (!fs::exists(source)) {
            m_errorString = "Source directory does not exist";
            return false;
        }

        if (!fs::is_directory(source)) {
            m_errorString = "Source path is not a directory";
            return false;
        }

        // Create the destination directory if it doesn't exist
        if (!fs::exists(destination)) {
            if (!fs::create_directories(destination)) {
                m_errorString = "Failed to create destination directory";
                return false;
            }
        } else if (!fs::is_directory(destination)) {
            m_errorString = "Destination path is not a directory";
            return false;
        }

        // Copy the directory contents
        for (const auto& entry : fs::directory_iterator(source)) {
            const auto& sourcePath = entry.path();
            const auto& destinationPath = fs::path(destination) / sourcePath.filename();

            if (fs::is_directory(sourcePath)) {
                if (recursive) {
                    if (!copyDirectory(sourcePath.string(), destinationPath.string(), recursive)) {
                        return false;
                    }
                }
            } else {
                fs::copy_file(sourcePath, destinationPath, fs::copy_options::overwrite_existing);
            }
        }

        return true;
    } catch (const std::exception& e) {
        m_errorString = e.what();
        return false;
    }
}

bool FileSystem::moveDirectory(const std::string& source, const std::string& destination) {
    try {
        if (!fs::exists(source)) {
            m_errorString = "Source directory does not exist";
            return false;
        }

        if (!fs::is_directory(source)) {
            m_errorString = "Source path is not a directory";
            return false;
        }

        if (fs::exists(destination)) {
            m_errorString = "Destination directory already exists";
            return false;
        }

        fs::rename(source, destination);
        return true;
    } catch (const std::exception& e) {
        m_errorString = e.what();
        return false;
    }
}

bool FileSystem::fileExists(const std::string& path) {
    try {
        return fs::exists(path) && fs::is_regular_file(path);
    } catch (const std::exception& e) {
        m_errorString = e.what();
        return false;
    }
}

bool FileSystem::directoryExists(const std::string& path) {
    try {
        return fs::exists(path) && fs::is_directory(path);
    } catch (const std::exception& e) {
        m_errorString = e.what();
        return false;
    }
}

FileInfo FileSystem::getFileInfo(const std::string& path) {
    return FileInfo(path);
}

std::vector<FileInfo> FileSystem::getDirectoryEntries(
    const std::string& path,
    const std::vector<std::string>& nameFilters,
    bool includeFiles,
    bool includeDirs,
    bool includeHidden)
{
    try {
        Folder folder(path);
        return folder.entryList(nameFilters, includeFiles, includeDirs, includeHidden);
    } catch (const std::exception& e) {
        m_errorString = e.what();
        return {};
    }
}

std::shared_ptr<File> FileSystem::openFile(const std::string& path, File::OpenMode mode) {
    auto file = std::make_shared<File>(path);
    if (!file->open(mode)) {
        m_errorString = file->errorString();
        return nullptr;
    }
    return file;
}

std::shared_ptr<Folder> FileSystem::openDirectory(const std::string& path) {
    auto folder = std::make_shared<Folder>(path);
    if (!folder->exists()) {
        m_errorString = "Directory does not exist";
        return nullptr;
    }
    return folder;
}

std::string FileSystem::currentDirectory() {
    try {
        return fs::current_path().string();
    } catch (const std::exception& e) {
        m_errorString = e.what();
        return {};
    }
}

bool FileSystem::setCurrentDirectory(const std::string& path) {
    try {
        fs::current_path(path);
        return true;
    } catch (const std::exception& e) {
        m_errorString = e.what();
        return false;
    }
}

std::string FileSystem::errorString() const {
    return m_errorString;
}

} // namespace filesystem
