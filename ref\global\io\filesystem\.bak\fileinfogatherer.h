﻿#ifndef FILEINFOGATHERER_H
#define FILEINFOGATHERER_H

#include <QObject>
#include <QDirIterator>
#include <QElapsedTimer>

class FileInfoGatherer : public QObject
{
    Q_OBJECT
public:
    explicit FileInfoGatherer(QObject* parent = nullptr);
    ~FileInfoGatherer();

    void getFileInfos(const QString& path);
    void getFiles(const QString& path, int& level, QElapsedTimer& base, bool& firstTime);
    void fetch(const QString& path, const QFileInfo& fileInfo, QElapsedTimer& base, bool& firstTime);
    std::vector<QFileInfo> updatedFiles() const { return m_updatedFiles; }

signals:
    void updates(const QString& path, const std::vector<QFileInfo>& updates);
    void directoryLoaded(const QString& path);

private:
    QAtomicInt m_abort;
    std::vector<QString> m_allFiles;
    std::vector<QFileInfo> m_updatedFiles;
};

#endif // FILEINFOGATHERER_H
