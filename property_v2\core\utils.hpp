#pragma once

/**
 * @file utils.hpp
 * @brief 现代化属性系统的工具函数
 * 
 * 提供通用的工具函数和宏定义。
 * 
 * <AUTHOR> System V2
 * @date 2025
 */

#include <source_location>
#include <string_view>
#include <utility>
#include <thread>
#include <chrono>

namespace property_v2::core {

/**
 * @brief 源代码位置信息
 * 
 * 封装C++20的std::source_location，提供调试信息。
 */
class source_location {
public:
    /**
     * @brief 默认构造函数
     */
    constexpr source_location() noexcept = default;
    
    /**
     * @brief 从std::source_location构造
     */
    constexpr source_location(const std::source_location& loc) noexcept
        : file_name_(loc.file_name())
        , function_name_(loc.function_name())
        , line_(loc.line())
        , column_(loc.column()) {}
    
    /**
     * @brief 获取当前位置
     * @param loc 源位置（默认为当前位置）
     * @return 源位置对象
     */
    static consteval source_location current(
        const std::source_location& loc = std::source_location::current()) noexcept {
        return source_location(loc);
    }
    
    /**
     * @brief 获取文件名
     */
    constexpr std::string_view file_name() const noexcept { return file_name_; }
    
    /**
     * @brief 获取函数名
     */
    constexpr std::string_view function_name() const noexcept { return function_name_; }
    
    /**
     * @brief 获取行号
     */
    constexpr std::uint32_t line() const noexcept { return line_; }
    
    /**
     * @brief 获取列号
     */
    constexpr std::uint32_t column() const noexcept { return column_; }

private:
    const char* file_name_ = "";
    const char* function_name_ = "";
    std::uint32_t line_ = 0;
    std::uint32_t column_ = 0;
};

/**
 * @brief 默认绑定位置宏
 */
#define PROPERTY_DEFAULT_BINDING_LOCATION \
    ::property_v2::core::source_location::current()

/**
 * @brief 线程ID类型
 */
using thread_id = std::thread::id;

/**
 * @brief 获取当前线程ID
 */
inline thread_id current_thread_id() noexcept {
    return std::this_thread::get_id();
}

/**
 * @brief 时间戳类型
 */
using timestamp = std::chrono::steady_clock::time_point;

/**
 * @brief 获取当前时间戳
 */
inline timestamp current_timestamp() noexcept {
    return std::chrono::steady_clock::now();
}

/**
 * @brief 计算时间差（毫秒）
 */
inline std::chrono::milliseconds time_diff_ms(timestamp start, timestamp end) noexcept {
    return std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
}

/**
 * @brief 禁用拷贝宏
 */
#define PROPERTY_DISABLE_COPY(ClassName) \
    ClassName(const ClassName&) = delete; \
    ClassName& operator=(const ClassName&) = delete;

/**
 * @brief 禁用移动宏
 */
#define PROPERTY_DISABLE_MOVE(ClassName) \
    ClassName(ClassName&&) = delete; \
    ClassName& operator=(ClassName&&) = delete;

/**
 * @brief 禁用拷贝和移动宏
 */
#define PROPERTY_DISABLE_COPY_MOVE(ClassName) \
    PROPERTY_DISABLE_COPY(ClassName) \
    PROPERTY_DISABLE_MOVE(ClassName)

/**
 * @brief 强制内联宏
 */
#if defined(__GNUC__) || defined(__clang__)
    #define PROPERTY_FORCE_INLINE inline __attribute__((always_inline))
#elif defined(_MSC_VER)
    #define PROPERTY_FORCE_INLINE __forceinline
#else
    #define PROPERTY_FORCE_INLINE inline
#endif

/**
 * @brief 可能未使用的变量标记
 */
#define PROPERTY_MAYBE_UNUSED [[maybe_unused]]

/**
 * @brief 预期分支提示
 */
#define PROPERTY_LIKELY [[likely]]
#define PROPERTY_UNLIKELY [[unlikely]]

/**
 * @brief 值交换函数
 * @tparam T 值类型
 * @param a 第一个值
 * @param b 第二个值
 */
template<typename T>
constexpr void swap_values(T& a, T& b) noexcept(std::is_nothrow_swappable_v<T>) {
    using std::swap;
    swap(a, b);
}

/**
 * @brief 指针交换函数
 * @tparam T 指针类型
 * @param lhs 第一个指针
 * @param rhs 第二个指针
 */
template<typename T>
constexpr void swap_pointers(T*& lhs, T*& rhs) noexcept {
    T* tmp = lhs;
    lhs = rhs;
    rhs = tmp;
}

/**
 * @brief 最小值函数
 * @tparam T 值类型
 * @param a 第一个值
 * @param b 第二个值
 * @return 较小的值
 */
template<typename T>
constexpr const T& min_value(const T& a, const T& b) noexcept {
    return (a < b) ? a : b;
}

/**
 * @brief 最大值函数
 * @tparam T 值类型
 * @param a 第一个值
 * @param b 第二个值
 * @return 较大的值
 */
template<typename T>
constexpr const T& max_value(const T& a, const T& b) noexcept {
    return (a < b) ? b : a;
}

/**
 * @brief 边界限制函数
 * @tparam T 值类型
 * @param min_val 最小值
 * @param val 当前值
 * @param max_val 最大值
 * @return 限制在边界内的值
 */
template<typename T>
constexpr const T& clamp_value(const T& min_val, const T& val, const T& max_val) noexcept {
    return max_value(min_val, min_value(val, max_val));
}

/**
 * @brief 哈希组合函数
 * @tparam T 值类型
 * @param seed 种子值
 * @param value 要哈希的值
 */
template<typename T>
void hash_combine(std::size_t& seed, const T& value) noexcept {
    std::hash<T> hasher;
    seed ^= hasher(value) + 0x9e3779b9 + (seed << 6) + (seed >> 2);
}

/**
 * @brief 多值哈希函数
 * @tparam Args 参数类型
 * @param args 要哈希的值
 * @return 组合哈希值
 */
template<typename... Args>
std::size_t hash_values(const Args&... args) noexcept {
    std::size_t seed = 0;
    (hash_combine(seed, args), ...);
    return seed;
}

/**
 * @brief 断言宏（仅在调试模式下有效）
 */
#ifndef NDEBUG
    #define PROPERTY_ASSERT(condition, message) \
        do { \
            if (!(condition)) { \
                std::cerr << "Assertion failed: " << (message) \
                         << " at " << __FILE__ << ":" << __LINE__ << std::endl; \
                std::abort(); \
            } \
        } while (false)
#else
    #define PROPERTY_ASSERT(condition, message) ((void)0)
#endif

/**
 * @brief 调试输出宏
 */
#ifndef NDEBUG
    #define PROPERTY_DEBUG_PRINT(message) \
        std::cout << "[DEBUG] " << (message) << std::endl
#else
    #define PROPERTY_DEBUG_PRINT(message) ((void)0)
#endif

} // namespace property_v2::core
