#pragma once

/**
 * @file type_traits.hpp
 * @brief 现代化属性系统的类型特征定义
 * 
 * 提供类型特征和元编程工具，支持编译时类型检查和优化。
 * 
 * <AUTHOR> System V2
 * @date 2025
 */

#include <type_traits>
#include <concepts>
#include <string>
#include <string_view>
#include <memory>
#include <functional>

namespace property_v2::core {

/**
 * @brief 类型提升特征，用于确定两个类型的公共类型
 * @tparam T 第一个类型
 * @tparam U 第二个类型
 */
template<typename T, typename U>
using promoted_t = std::common_type_t<T, U>;

/**
 * @brief 检查类型是否具有相等比较运算符
 * @tparam T 要检查的类型
 */
template<typename T>
struct has_equality_operator : std::false_type {};

template<typename T>
    requires requires(const T& a, const T& b) { { a == b } -> std::convertible_to<bool>; }
struct has_equality_operator<T> : std::true_type {};

template<typename T>
inline constexpr bool has_equality_operator_v = has_equality_operator<T>::value;

/**
 * @brief 检查类型是否可解引用
 * @tparam T 要检查的类型
 */
template<typename T>
struct is_dereferenceable : std::false_type {};

template<typename T>
    requires requires(T t) { *t; t.operator->(); }
struct is_dereferenceable<T> : std::true_type {};

template<typename T>
inline constexpr bool is_dereferenceable_v = is_dereferenceable<T>::value;

/**
 * @brief 检查类型是否为字符串类型
 * @tparam T 要检查的类型
 */
template<typename T>
struct is_string : std::false_type {};

template<>
struct is_string<std::string> : std::true_type {};

template<>
struct is_string<std::wstring> : std::true_type {};

template<>
struct is_string<std::u8string> : std::true_type {};

template<>
struct is_string<std::u16string> : std::true_type {};

template<>
struct is_string<std::u32string> : std::true_type {};

template<>
struct is_string<std::string_view> : std::true_type {};

template<>
struct is_string<std::wstring_view> : std::true_type {};

template<>
struct is_string<std::u8string_view> : std::true_type {};

template<>
struct is_string<std::u16string_view> : std::true_type {};

template<>
struct is_string<std::u32string_view> : std::true_type {};

template<>
struct is_string<const char*> : std::true_type {};

template<>
struct is_string<char*> : std::true_type {};

template<typename T>
inline constexpr bool is_string_v = is_string<std::remove_cv_t<T>>::value;

/**
 * @brief 参数类型特征，决定是否使用引用传递
 * @tparam T 值类型
 */
template<typename T>
struct parameter_type {
    static constexpr bool use_references = 
        !(std::is_arithmetic_v<T> || std::is_enum_v<T> || std::is_pointer_v<T>);
    
    using type = std::conditional_t<use_references, const T&, T>;
};

template<typename T>
using parameter_type_t = typename parameter_type<T>::type;

/**
 * @brief 右值引用类型特征
 * @tparam T 值类型
 */
template<typename T>
struct rvalue_reference_type {
    static constexpr bool use_references = parameter_type<T>::use_references;
    
    struct disabled_rvalue_refs {};
    using type = std::conditional_t<use_references, T&&, disabled_rvalue_refs>;
};

template<typename T>
using rvalue_reference_type_t = typename rvalue_reference_type<T>::type;

/**
 * @brief 箭头运算符结果类型特征
 * @tparam T 值类型
 */
template<typename T>
struct arrow_operator_result {
    using type = std::conditional_t<
        std::is_pointer_v<T>, 
        const T&,
        std::conditional_t<is_dereferenceable_v<T>, const T&, void>
    >;
};

template<typename T>
using arrow_operator_result_t = typename arrow_operator_result<T>::type;

/**
 * @brief 函数特征，提取函数的返回类型和参数类型
 * @tparam F 函数类型
 */
template<typename F>
struct function_traits;

template<typename R, typename... Args>
struct function_traits<R(Args...)> {
    using return_type = R;
    using argument_types = std::tuple<Args...>;
    static constexpr std::size_t arity = sizeof...(Args);
    
    template<std::size_t N>
    using argument_type = std::tuple_element_t<N, argument_types>;
};

template<typename R, typename... Args>
struct function_traits<R(*)(Args...)> : function_traits<R(Args...)> {};

template<typename R, typename C, typename... Args>
struct function_traits<R(C::*)(Args...)> : function_traits<R(Args...)> {};

template<typename R, typename C, typename... Args>
struct function_traits<R(C::*)(Args...) const> : function_traits<R(Args...)> {};

template<typename F>
    requires requires { &F::operator(); }
struct function_traits<F> : function_traits<decltype(&F::operator())> {};

/**
 * @brief 检查类型是否为可调用对象且返回指定类型
 * @tparam F 函数类型
 * @tparam R 期望的返回类型
 */
template<typename F, typename R>
struct is_invocable_r : std::false_type {};

template<typename F, typename R>
    requires std::invocable<F> && std::convertible_to<std::invoke_result_t<F>, R>
struct is_invocable_r<F, R> : std::true_type {};

template<typename F, typename R>
inline constexpr bool is_invocable_r_v = is_invocable_r<F, R>::value;

/**
 * @brief 移除引用和CV限定符的类型别名
 * @tparam T 原始类型
 */
template<typename T>
using remove_cvref_t = std::remove_cv_t<std::remove_reference_t<T>>;

/**
 * @brief 检查类型是否为智能指针
 * @tparam T 要检查的类型
 */
template<typename T>
struct is_smart_pointer : std::false_type {};

template<typename T>
struct is_smart_pointer<std::shared_ptr<T>> : std::true_type {};

template<typename T>
struct is_smart_pointer<std::unique_ptr<T>> : std::true_type {};

template<typename T, typename D>
struct is_smart_pointer<std::unique_ptr<T, D>> : std::true_type {};

template<typename T>
struct is_smart_pointer<std::weak_ptr<T>> : std::true_type {};

template<typename T>
inline constexpr bool is_smart_pointer_v = is_smart_pointer<T>::value;

} // namespace property_v2::core
