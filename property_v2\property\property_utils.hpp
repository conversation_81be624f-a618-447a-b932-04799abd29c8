#pragma once

/**
 * @file property_utils.hpp
 * @brief 属性系统工具函数
 * 
 * 提供属性系统的便利函数和工具。
 * 
 * <AUTHOR> System V2
 * @date 2025
 */

#include <functional>
#include <utility>
#include <vector>
#include <string>
#include "property.hpp"
#include "../binding/binding_data.hpp"
#include "../core/concepts.hpp"

namespace property_v2::property {

/**
 * @brief 属性组合器
 * 
 * 用于组合多个属性的值。
 */
namespace combinators {

/**
 * @brief 组合两个属性的值
 * 
 * @tparam T1 第一个属性类型
 * @tparam T2 第二个属性类型
 * @tparam F 组合函数类型
 * @param prop1 第一个属性
 * @param prop2 第二个属性
 * @param combiner 组合函数
 * @param location 源位置
 * @return 组合后的绑定
 */
template<typename T1, typename T2, typename F>
    requires core::Invocable_r<F, std::invoke_result_t<F, T1, T2>, T1, T2>
auto combine(const property<T1>& prop1, const property<T2>& prop2, F&& combiner,
            const core::source_location& location = PROPERTY_DEFAULT_BINDING_LOCATION) {
    using result_type = std::invoke_result_t<F, T1, T2>;
    return binding::make_property_binding([&prop1, &prop2, combiner = std::forward<F>(combiner)]() -> result_type {
        return combiner(prop1.value(), prop2.value());
    }, location);
}

/**
 * @brief 组合三个属性的值
 * 
 * @tparam T1 第一个属性类型
 * @tparam T2 第二个属性类型
 * @tparam T3 第三个属性类型
 * @tparam F 组合函数类型
 * @param prop1 第一个属性
 * @param prop2 第二个属性
 * @param prop3 第三个属性
 * @param combiner 组合函数
 * @param location 源位置
 * @return 组合后的绑定
 */
template<typename T1, typename T2, typename T3, typename F>
    requires core::Invocable_r<F, std::invoke_result_t<F, T1, T2, T3>, T1, T2, T3>
auto combine(const property<T1>& prop1, const property<T2>& prop2, const property<T3>& prop3, 
            F&& combiner, const core::source_location& location = PROPERTY_DEFAULT_BINDING_LOCATION) {
    using result_type = std::invoke_result_t<F, T1, T2, T3>;
    return binding::make_property_binding([&prop1, &prop2, &prop3, combiner = std::forward<F>(combiner)]() -> result_type {
        return combiner(prop1.value(), prop2.value(), prop3.value());
    }, location);
}

/**
 * @brief 映射属性值
 * 
 * @tparam T 源属性类型
 * @tparam F 映射函数类型
 * @param source_prop 源属性
 * @param mapper 映射函数
 * @param location 源位置
 * @return 映射后的绑定
 */
template<typename T, typename F>
    requires core::Invocable_r<F, std::invoke_result_t<F, T>, T>
auto map(const property<T>& source_prop, F&& mapper,
        const core::source_location& location = PROPERTY_DEFAULT_BINDING_LOCATION) {
    using result_type = std::invoke_result_t<F, T>;
    return binding::make_property_binding([&source_prop, mapper = std::forward<F>(mapper)]() -> result_type {
        return mapper(source_prop.value());
    }, location);
}

/**
 * @brief 过滤属性值
 * 
 * 只有当谓词返回true时，才使用新值，否则保持原值。
 * 
 * @tparam T 属性类型
 * @tparam Predicate 谓词类型
 * @param source_prop 源属性
 * @param predicate 谓词函数
 * @param default_value 默认值
 * @param location 源位置
 * @return 过滤后的绑定
 */
template<typename T, typename Predicate>
    requires core::Invocable_r<Predicate, bool, T>
auto filter(const property<T>& source_prop, Predicate&& predicate, T default_value,
           const core::source_location& location = PROPERTY_DEFAULT_BINDING_LOCATION) {
    return binding::make_property_binding([&source_prop, predicate = std::forward<Predicate>(predicate), 
                                         default_value = std::move(default_value)]() -> T {
        T current_value = source_prop.value();
        return predicate(current_value) ? current_value : default_value;
    }, location);
}

} // namespace combinators

/**
 * @brief 属性验证器
 */
namespace validators {

/**
 * @brief 范围验证器
 * 
 * @tparam T 值类型
 * @param min_value 最小值
 * @param max_value 最大值
 * @return 验证函数
 */
template<typename T>
    requires std::totally_ordered<T>
auto range(T min_value, T max_value) {
    return [min_value, max_value](const T& value) -> bool {
        return value >= min_value && value <= max_value;
    };
}

/**
 * @brief 非空验证器
 * 
 * @tparam T 值类型
 * @return 验证函数
 */
template<typename T>
auto not_empty() {
    if constexpr (requires(T t) { t.empty(); }) {
        return [](const T& value) -> bool {
            return !value.empty();
        };
    } else {
        return [](const T&) -> bool {
            return true;  // 对于不支持empty()的类型，总是有效
        };
    }
}

/**
 * @brief 自定义验证器
 * 
 * @tparam Predicate 谓词类型
 * @param predicate 谓词函数
 * @return 验证函数
 */
template<typename Predicate>
auto custom(Predicate&& predicate) {
    return std::forward<Predicate>(predicate);
}

} // namespace validators

/**
 * @brief 属性转换器
 */
namespace converters {

/**
 * @brief 字符串转换器
 * 
 * @tparam T 源类型
 * @return 转换函数
 */
template<typename T>
auto to_string() {
    return [](const T& value) -> std::string {
        if constexpr (core::is_string_v<T>) {
            return std::string(value);
        } else if constexpr (std::is_arithmetic_v<T>) {
            return std::to_string(value);
        } else if constexpr (requires { std::to_string(value); }) {
            return std::to_string(value);
        } else {
            return typeid(T).name();
        }
    };
}

/**
 * @brief 数值转换器
 * 
 * @tparam Target 目标类型
 * @tparam Source 源类型
 * @return 转换函数
 */
template<typename Target, typename Source>
    requires std::convertible_to<Source, Target>
auto to_numeric() {
    return [](const Source& value) -> Target {
        return static_cast<Target>(value);
    };
}

/**
 * @brief 布尔转换器
 * 
 * @tparam T 源类型
 * @return 转换函数
 */
template<typename T>
auto to_bool() {
    return [](const T& value) -> bool {
        if constexpr (std::is_arithmetic_v<T>) {
            return value != T{};
        } else if constexpr (requires { value.empty(); }) {
            return !value.empty();
        } else if constexpr (requires { static_cast<bool>(value); }) {
            return static_cast<bool>(value);
        } else {
            return true;  // 默认为true
        }
    };
}

} // namespace converters

/**
 * @brief 属性调试工具
 */
namespace debug {

/**
 * @brief 打印属性值变更
 * 
 * @tparam T 属性类型
 * @param prop 属性
 * @param name 属性名称
 * @return 变更处理器
 */
template<typename T>
auto trace_changes(const property<T>& prop, const std::string& name) {
    return prop.on_value_changed([&prop, name]() {
        std::cout << "[TRACE] Property '" << name << "' changed to: " 
                  << prop.to_string() << std::endl;
    });
}

/**
 * @brief 验证属性值
 * 
 * @tparam T 属性类型
 * @tparam Validator 验证器类型
 * @param prop 属性
 * @param validator 验证器
 * @param name 属性名称
 * @return 变更处理器
 */
template<typename T, typename Validator>
    requires core::Invocable_r<Validator, bool, T>
auto validate_changes(const property<T>& prop, Validator&& validator, const std::string& name) {
    return prop.on_value_changed([&prop, validator = std::forward<Validator>(validator), name]() {
        if (!validator(prop.value())) {
            std::cerr << "[VALIDATION] Property '" << name << "' has invalid value: " 
                      << prop.to_string() << std::endl;
        }
    });
}

} // namespace debug

} // namespace property_v2::property
