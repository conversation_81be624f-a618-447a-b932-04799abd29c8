#pragma once

#include "types.h"
#include "property_data.h"
#include <functional>
#include <memory>
#include <vector>
#include <any>

/**
 * @brief Modern Property System - Property Binding
 * 
 * This file contains the property binding system that manages
 * dependencies between properties and automatic value propagation.
 */

namespace modern_property {

// Forward declarations
class binding_evaluator;
class dependency_tracker;

/**
 * @brief Error information for binding evaluation failures
 */
struct binding_error {
    errors::binding_error_type type{errors::binding_error_type::none};
    string_type message;
    source_location_type location;
    
    /// @brief Check if this represents an error state
    [[nodiscard]] bool has_error() const noexcept {
        return type != errors::binding_error_type::none;
    }
    
    /// @brief Create a no-error state
    static binding_error none() noexcept {
        return {};
    }
    
    /// @brief Create an error state
    static binding_error create(errors::binding_error_type error_type, 
                               const string_type& msg,
                               const source_location_type& loc = source_location_type::current()) {
        return {error_type, msg, loc};
    }
};

/**
 * @brief Base class for untyped property bindings
 * 
 * This class provides type-erased storage and evaluation of property bindings.
 * It uses modern C++20 features like concepts and std::function for type safety.
 */
class untyped_property_binding {
public:
    /// @brief Function type for binding evaluation
    using evaluation_function = std::function<bool(untyped_property_data*)>;
    
    /// @brief Function type for binding destruction
    using destruction_function = std::function<void()>;

    /**
     * @brief Default constructor creates null binding
     */
    untyped_property_binding() = default;
    
    /**
     * @brief Constructor with evaluation function
     * @param eval_func Function to evaluate this binding
     * @param location Source location for debugging
     */
    explicit untyped_property_binding(evaluation_function eval_func,
                                    const source_location_type& location = source_location_type::current())
        : evaluation_func_{std::move(eval_func)}, location_{location} {}
    
    /// @brief Move constructor
    untyped_property_binding(untyped_property_binding&&) = default;
    
    /// @brief Move assignment
    untyped_property_binding& operator=(untyped_property_binding&&) = default;
    
    /// @brief Destructor
    ~untyped_property_binding() = default;
    
    MODERN_PROPERTY_DISABLE_COPY(untyped_property_binding)

    /**
     * @brief Check if this binding is null/empty
     * @return true if null, false if valid
     */
    [[nodiscard]] bool is_null() const noexcept {
        return !evaluation_func_;
    }
    
    /**
     * @brief Evaluate this binding for given property data
     * @param target_property Property to update with binding result
     * @return true if property value changed, false otherwise
     */
    bool evaluate(untyped_property_data* target_property) const {
        if (is_null() || !target_property) {
            return false;
        }
        
        try {
            return evaluation_func_(target_property);
        } catch (const std::exception& e) {
            last_error_ = binding_error::create(
                errors::binding_error_type::evaluation_failed,
                string_type{"Binding evaluation failed: "} + e.what(),
                location_
            );
            return false;
        } catch (...) {
            last_error_ = binding_error::create(
                errors::binding_error_type::evaluation_failed,
                "Binding evaluation failed: unknown exception",
                location_
            );
            return false;
        }
    }
    
    /**
     * @brief Get the last error that occurred during evaluation
     * @return Error information
     */
    [[nodiscard]] const binding_error& last_error() const noexcept {
        return last_error_;
    }
    
    /**
     * @brief Get source location where this binding was created
     * @return Source location for debugging
     */
    [[nodiscard]] const source_location_type& source_location() const noexcept {
        return location_;
    }
    
    /**
     * @brief Check if this binding is currently being evaluated (circular dependency detection)
     * @return true if currently evaluating
     */
    [[nodiscard]] bool is_evaluating() const noexcept {
        return is_evaluating_;
    }

protected:
    /**
     * @brief Set evaluation flag (used by binding evaluator)
     * @param evaluating New evaluation state
     */
    void set_evaluating(bool evaluating) const noexcept {
        is_evaluating_ = evaluating;
    }
    
    friend class binding_evaluator;

private:
    evaluation_function evaluation_func_;
    source_location_type location_;
    mutable binding_error last_error_;
    mutable bool is_evaluating_{false};
};

/**
 * @brief Typed property binding with compile-time type safety
 * @tparam T The type of value this binding produces
 */
template<Property_storable T>
class property_binding : public untyped_property_binding {
public:
    /// @brief Value type produced by this binding
    using value_type = T;
    
    /// @brief Function type for typed binding evaluation
    using typed_function = std::function<T()>;
    
    /// @brief Parameter types from property_data
    using const_param_type = typename parameter_traits<T>::const_param_type;

    /**
     * @brief Default constructor creates null binding
     */
    property_binding() = default;
    
    /**
     * @brief Constructor with typed function
     * @tparam F Function type (deduced)
     * @param func Function that computes the binding value
     * @param location Source location for debugging
     */
    template<typename F>
    explicit property_binding(F&& func, 
                            const source_location_type& location = source_location_type::current())
        requires Property_binding_function<F, T>
        : untyped_property_binding{create_evaluation_function(std::forward<F>(func)), location} {}
    
    /// @brief Move constructor
    property_binding(property_binding&&) = default;
    
    /// @brief Move assignment
    property_binding& operator=(property_binding&&) = default;
    
    /// @brief Destructor
    ~property_binding() = default;
    
    MODERN_PROPERTY_DISABLE_COPY(property_binding)
    
    /**
     * @brief Create property binding from lambda or function object
     * @tparam F Function type
     * @param func Function that computes binding value
     * @param location Source location for debugging
     * @return New property binding
     */
    template<typename F>
    static property_binding create(F&& func, 
                                 const source_location_type& location = source_location_type::current())
        requires Property_binding_function<F, T> {
        return property_binding{std::forward<F>(func), location};
    }

private:
    /**
     * @brief Create type-erased evaluation function from typed function
     * @tparam F Function type
     * @param func Typed function
     * @return Type-erased evaluation function
     */
    template<typename F>
    static evaluation_function create_evaluation_function(F&& func)
        requires Property_binding_function<F, T> {
        
        // Store the function in a shared_ptr to allow copying the evaluation_function
        auto stored_func = std::make_shared<std::decay_t<F>>(std::forward<F>(func));
        
        return [stored_func](untyped_property_data* target) -> bool {
            auto* typed_target = property_data_utils::safe_cast<T>(target);
            if (!typed_target) {
                throw errors::property_binding_exception{
                    errors::binding_error_type::type_mismatch,
                    "Target property type does not match binding type"
                };
            }
            
            T new_value = (*stored_func)();
            return typed_target->update_from_binding(std::move(new_value));
        };
    }
};

/**
 * @brief Factory function to create property bindings with type deduction
 * @tparam F Function type (deduced)
 * @param func Function that computes binding value
 * @param location Source location for debugging
 * @return New property binding with deduced return type
 */
template<typename F>
auto make_property_binding(F&& func, 
                          const source_location_type& location = source_location_type::current())
    requires std::is_invocable_v<F> {
    using return_type = std::invoke_result_t<F>;
    return property_binding<return_type>::create(std::forward<F>(func), location);
}

/**
 * @brief Dependency tracker manages property dependencies during binding evaluation
 */
class dependency_tracker {
public:
    /// @brief Container type for storing dependencies
    using dependency_list = std::vector<const untyped_property_data*>;

    /**
     * @brief Constructor
     */
    dependency_tracker() = default;
    
    /// @brief Destructor
    ~dependency_tracker() = default;
    
    MODERN_PROPERTY_DISABLE_COPY_MOVE(dependency_tracker)

    /**
     * @brief Register a property as a dependency
     * @param property Property to register as dependency
     */
    void register_dependency(const untyped_property_data* property) {
        if (property && !is_already_registered(property)) {
            dependencies_.push_back(property);
        }
    }
    
    /**
     * @brief Get all registered dependencies
     * @return List of dependencies
     */
    [[nodiscard]] const dependency_list& dependencies() const noexcept {
        return dependencies_;
    }
    
    /**
     * @brief Clear all registered dependencies
     */
    void clear() noexcept {
        dependencies_.clear();
    }
    
    /**
     * @brief Check if dependencies were registered
     * @return true if any dependencies were registered
     */
    [[nodiscard]] bool has_dependencies() const noexcept {
        return !dependencies_.empty();
    }

private:
    /**
     * @brief Check if a property is already registered as dependency
     * @param property Property to check
     * @return true if already registered
     */
    bool is_already_registered(const untyped_property_data* property) const noexcept {
        return std::find(dependencies_.begin(), dependencies_.end(), property) != dependencies_.end();
    }

    dependency_list dependencies_;
};

/**
 * @brief Binding evaluator manages the evaluation of property bindings with circular dependency detection
 */
class binding_evaluator {
public:
    /// @brief Container for tracking evaluation stack
    using evaluation_stack = std::vector<const untyped_property_binding*>;

    /**
     * @brief Constructor
     */
    binding_evaluator() = default;
    
    /// @brief Destructor  
    ~binding_evaluator() = default;
    
    MODERN_PROPERTY_DISABLE_COPY_MOVE(binding_evaluator)

    /**
     * @brief Evaluate a binding with circular dependency detection
     * @param binding Binding to evaluate
     * @param target_property Target property to update
     * @param tracker Dependency tracker to use
     * @return binding_error indicating success or failure
     */
    binding_error evaluate_binding(const untyped_property_binding& binding,
                                 untyped_property_data* target_property,
                                 dependency_tracker& tracker) {
        
        // Check for circular dependency
        if (is_in_evaluation_stack(&binding)) {
            return binding_error::create(
                errors::binding_error_type::circular_dependency,
                "Circular dependency detected in property binding",
                binding.source_location()
            );
        }
        
        // Add to evaluation stack
        evaluation_stack_.push_back(&binding);
        binding.set_evaluating(true);
        
        // Clear previous dependencies
        tracker.clear();
        
        // Set current tracker (for dependency registration)
        auto* previous_tracker = current_tracker_;
        current_tracker_ = &tracker;
        
        // Evaluate binding
        bool changed = false;
        binding_error result = binding_error::none();
        
        try {
            changed = binding.evaluate(target_property);
        } catch (const errors::property_binding_exception& e) {
            result = binding_error::create(e.error_type(), e.what(), e.source_location());
        } catch (const std::exception& e) {
            result = binding_error::create(
                errors::binding_error_type::evaluation_failed,
                e.what(),
                binding.source_location()
            );
        }
        
        // Restore previous tracker
        current_tracker_ = previous_tracker;
        
        // Remove from evaluation stack
        binding.set_evaluating(false);
        evaluation_stack_.pop_back();
        
        return result;
    }
    
    /**
     * @brief Register current property as dependency (called during evaluation)
     * @param property Property to register
     */
    void register_current_dependency(const untyped_property_data* property) {
        if (current_tracker_) {
            current_tracker_->register_dependency(property);
        }
    }
    
    /**
     * @brief Get current dependency tracker
     * @return Current tracker or nullptr if none
     */
    [[nodiscard]] dependency_tracker* current_dependency_tracker() const noexcept {
        return current_tracker_;
    }

private:
    /**
     * @brief Check if binding is currently in the evaluation stack
     * @param binding Binding to check
     * @return true if in stack (circular dependency)
     */
    bool is_in_evaluation_stack(const untyped_property_binding* binding) const noexcept {
        return std::find(evaluation_stack_.begin(), evaluation_stack_.end(), binding) != evaluation_stack_.end();
    }

    evaluation_stack evaluation_stack_;
    dependency_tracker* current_tracker_{nullptr};
};

} // namespace modern_property 