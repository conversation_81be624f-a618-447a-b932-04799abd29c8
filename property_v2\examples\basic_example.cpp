/**
 * @file basic_example.cpp
 * @brief Property System V2 基本用法示例
 * 
 * 展示现代化属性系统的基本功能，包括：
 * - 属性创建和值操作
 * - 属性绑定
 * - 观察者模式
 * - 错误处理
 * 
 * <AUTHOR> System V2
 * @date 2025
 */

#include "../property_v2.hpp"
#include <iostream>
#include <string>
#include <cassert>

using namespace property_v2;

/**
 * @brief 演示基本属性操作
 */
void demonstrate_basic_operations() {
    std::cout << "\n=== 基本属性操作 ===" << std::endl;
    
    // 创建属性
    property::property<int> x{42};
    property::property<std::string> name{"Hello"};
    
    // 读取值
    std::cout << "x = " << x.value() << std::endl;
    std::cout << "name = " << name.value() << std::endl;
    
    // 设置值
    x.set_value(100);
    name = "World";  // 使用赋值运算符
    
    std::cout << "After setting: x = " << x << ", name = " << name << std::endl;
    
    // 类型转换
    int auto_value = x;  // 自动转换
    std::cout << "Auto converted value: " << auto_value << std::endl;
}

/**
 * @brief 演示属性绑定
 */
void demonstrate_property_binding() {
    std::cout << "\n=== 属性绑定 ===" << std::endl;
    
    property::property<int> x{10};
    property::property<int> y{20};
    
    // 创建绑定属性
    property::property<int> sum;
    sum.set_binding([&x, &y]() { 
        return x.value() + y.value(); 
    });
    
    std::cout << "Initial: x=" << x << ", y=" << y << ", sum=" << sum << std::endl;
    
    // 改变 x，sum 会自动更新
    x = 15;
    std::cout << "After x=15: sum=" << sum << std::endl;
    
    // 改变 y，sum 也会自动更新
    y = 25;
    std::cout << "After y=25: sum=" << sum << std::endl;
    
    // 使用便利函数创建绑定
    auto product = property::property<int>{[&x, &y]() { 
        return x.value() * y.value(); 
    }};
    
    std::cout << "Product: " << product << std::endl;
}

/**
 * @brief 演示观察者模式
 */
void demonstrate_observers() {
    std::cout << "\n=== 观察者模式 ===" << std::endl;
    
    property::property<int> counter{0};
    
    // 创建变更处理器
    auto change_handler = counter.on_value_changed([]() {
        std::cout << "Counter changed!" << std::endl;
    });
    
    // 创建通知器（带值访问）
    auto notifier = counter.add_notifier([&counter]() {
        std::cout << "Counter is now: " << counter.value() << std::endl;
    });
    
    // 订阅（立即调用一次，然后监听变更）
    auto subscription = counter.subscribe([&counter]() {
        std::cout << "Subscription: counter = " << counter.value() << std::endl;
    });
    
    std::cout << "\nChanging counter values:" << std::endl;
    counter = 1;
    counter = 2;
    counter = 3;
}

/**
 * @brief 演示高级绑定功能
 */
void demonstrate_advanced_bindings() {
    std::cout << "\n=== 高级绑定功能 ===" << std::endl;
    
    property::property<int> a{5};
    property::property<int> b{10};
    property::property<int> c{2};
    
    // 组合多个属性
    auto sum_binding = property::combinators::combine(a, b, 
        [](int x, int y) { return x + y; });
    
    property::property<int> sum;
    sum.set_binding(sum_binding);
    
    std::cout << "a=" << a << ", b=" << b << ", sum=" << sum << std::endl;
    
    // 三个属性的组合
    auto complex_binding = property::combinators::combine(a, b, c,
        [](int x, int y, int z) { return x * y + z; });
    
    property::property<int> result;
    result.set_binding(complex_binding);
    
    std::cout << "Complex result (a*b+c): " << result << std::endl;
    
    // 映射属性值
    auto string_mapping = property::combinators::map(a, 
        [](int value) { return "Value: " + std::to_string(value); });
    
    property::property<std::string> mapped_string;
    mapped_string.set_binding(string_mapping);
    
    std::cout << "Mapped string: " << mapped_string << std::endl;
    
    // 改变源属性，观察绑定更新
    std::cout << "\nChanging a to 8:" << std::endl;
    a = 8;
    std::cout << "sum=" << sum << ", result=" << result << ", mapped=" << mapped_string << std::endl;
}

/**
 * @brief 演示性能监控
 */
void demonstrate_performance_monitoring() {
    std::cout << "\n=== 性能监控 ===" << std::endl;
    
    // 启用性能监控
    support::performance_monitor::instance().enable();
    support::performance_monitor::instance().reset();
    
    property::property<int> x{0};
    property::property<int> y;
    y.set_binding([&x]() { return x.value() * 2; });
    
    // 执行一些操作
    for (int i = 0; i < 1000; ++i) {
        x = i;
        volatile int temp = y.value();  // 防止优化
        (void)temp;
    }
    
    // 获取性能统计
    auto stats = support::performance_monitor::instance().get_stats();
    std::cout << "Performance Statistics:" << std::endl;
    std::cout << stats.to_string() << std::endl;
}

/**
 * @brief 演示错误处理
 */
void demonstrate_error_handling() {
    std::cout << "\n=== 错误处理 ===" << std::endl;
    
    // 设置错误处理策略为记录并继续
    auto& error_manager = support::error_manager::instance();
    auto default_handler = std::make_unique<support::default_error_handler>(
        support::error_handling_strategy::log_and_continue);
    error_manager.set_error_handler(std::move(default_handler));
    
    try {
        // 创建可能导致绑定循环的属性
        property::property<int> x{1};
        property::property<int> y{2};
        
        // 这种绑定在实际实现中应该被检测为循环
        x.set_binding([&y]() { return y.value() + 1; });
        y.set_binding([&x]() { return x.value() + 1; });
        
        std::cout << "Attempting to read values..." << std::endl;
        std::cout << "x = " << x.value() << std::endl;
        std::cout << "y = " << y.value() << std::endl;
        
    } catch (const support::property_exception& e) {
        std::cout << "Caught property exception: " << e.what() << std::endl;
        std::cout << "Location: " << e.source_location().file_name() 
                  << ":" << e.source_location().line() << std::endl;
    }
}

/**
 * @brief 主函数
 */
int main() {
    std::cout << "Property System V2 - Basic Example" << std::endl;
    std::cout << "Version: " << version::string << std::endl;
    std::cout << "Build: " << version::build_date << " " << version::build_time << std::endl;
    
    try {
        demonstrate_basic_operations();
        demonstrate_property_binding();
        demonstrate_observers();
        demonstrate_advanced_bindings();
        demonstrate_performance_monitoring();
        demonstrate_error_handling();
        
        std::cout << "\n=== 示例完成 ===" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
