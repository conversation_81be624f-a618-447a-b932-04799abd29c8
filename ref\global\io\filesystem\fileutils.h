﻿#ifndef FILEUTILS_H
#define FILEUTILS_H

#include <string>
#include <vector>

namespace fileutils {
std::string readableFileSize(const int bytes, const int precision=2);
std::vector<std::string> pathSplit(const std::string& input);
}

class FileUtils
{
public:
    FileUtils() = default;

    static bool showInGraphicalShell(const std::string& path);
    static bool open(const std::string& path);

protected:
    void showInExplorer(const std::string& path) const;
};

#endif // FILEUTILS_H
