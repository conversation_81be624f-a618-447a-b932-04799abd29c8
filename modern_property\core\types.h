#pragma once

#include <concepts>
#include <cstddef>
#include <type_traits>
#include <source_location>
#include <memory>
#include <atomic>
#include <span>

/**
 * @brief Modern Property System - Core Types
 * 
 * This file contains the fundamental type definitions and concepts
 * for the modern C++20 property binding system.
 */

namespace modern_property {

/// @brief Type alias for size type used throughout the system
using size_type = std::ptrdiff_t;

/// @brief Atomic reference counter type
using ref_count_type = std::atomic<std::uint32_t>;

/// @brief String type used for error messages and debugging
using string_type = std::string;

/// @brief Location information for debugging property bindings
using source_location_type = std::source_location;

/**
 * @brief Concept to check if a type has equality comparison
 * @tparam T Type to check
 */
template<typename T>
concept Equality_comparable = requires(const T& a, const T& b) {
    { a == b } -> std::convertible_to<bool>;
    { a != b } -> std::convertible_to<bool>;
};

/**
 * @brief Concept to check if a type is suitable for property storage
 * @tparam T Type to check
 */
template<typename T>
concept Property_storable = std::is_object_v<T> && 
                           std::is_destructible_v<T> &&
                           (std::is_copy_constructible_v<T> || std::is_move_constructible_v<T>);

/**
 * @brief Concept to check if a type is a valid property binding function
 * @tparam F Function type
 * @tparam R Expected return type
 */
template<typename F, typename R>
concept Property_binding_function = std::is_invocable_r_v<R, F>;

/**
 * @brief Concept for types that support efficient parameter passing
 * @tparam T Type to check
 */
template<typename T>
concept Efficient_parameter = std::is_arithmetic_v<T> || 
                             std::is_enum_v<T> || 
                             std::is_pointer_v<T> ||
                             sizeof(T) <= sizeof(void*);

/**
 * @brief Type trait to determine optimal parameter passing strategy
 * @tparam T The type to analyze
 */
template<Property_storable T>
struct parameter_traits {
    /// @brief Whether to use reference passing for this type
    static constexpr bool use_reference = !Efficient_parameter<T>;
    
    /// @brief Type to use for const parameters
    using const_param_type = std::conditional_t<use_reference, const T&, T>;
    
    /// @brief Type to use for mutable parameters  
    using param_type = std::conditional_t<use_reference, T&, T>;
    
    /// @brief Type to use for rvalue references (disabled for efficient types)
    using rvalue_type = std::conditional_t<use_reference, T&&, const T&>;
};

/**
 * @brief RAII helper for disabling copy and move operations
 */
#define MODERN_PROPERTY_DISABLE_COPY_MOVE(ClassName) \
    ClassName(const ClassName&) = delete; \
    ClassName& operator=(const ClassName&) = delete; \
    ClassName(ClassName&&) = delete; \
    ClassName& operator=(ClassName&&) = delete;

/**
 * @brief Helper macro for disabling copy operations only
 */
#define MODERN_PROPERTY_DISABLE_COPY(ClassName) \
    ClassName(const ClassName&) = delete; \
    ClassName& operator=(const ClassName&) = delete;

/**
 * @brief Memory alignment utilities for property data
 */
namespace memory {
    /**
     * @brief Calculate required alignment for tagged pointer storage
     * @tparam T Type to analyze
     */
    template<typename T>
    consteval size_type required_alignment() noexcept {
        return std::max(alignof(T), alignof(void*));
    }
    
    /**
     * @brief Check if a pointer is properly aligned for type T
     * @tparam T Target type
     * @param ptr Pointer to check
     */
    template<typename T>
    constexpr bool is_aligned(const void* ptr) noexcept {
        return reinterpret_cast<std::uintptr_t>(ptr) % alignof(T) == 0;
    }
}

/**
 * @brief Error handling types for property operations
 */
namespace errors {
    /// @brief Enumeration of possible property binding errors
    enum class binding_error_type : std::uint8_t {
        none = 0,
        circular_dependency,
        evaluation_failed,
        type_mismatch,
        invalid_binding,
        observer_error
    };
    
    /// @brief Exception type for property binding errors
    class property_binding_exception : public std::runtime_error {
    public:
        explicit property_binding_exception(binding_error_type type, 
                                          const string_type& message,
                                          const source_location_type& location = 
                                              source_location_type::current())
            : std::runtime_error{message}, error_type_{type}, location_{location} {}
            
        [[nodiscard]] binding_error_type error_type() const noexcept { 
            return error_type_; 
        }
        
        [[nodiscard]] const source_location_type& source_location() const noexcept { 
            return location_; 
        }
        
    private:
        binding_error_type error_type_;
        source_location_type location_;
    };
}

/**
 * @brief Forward declarations for core property system classes
 */
template<Property_storable T> class property_data;
template<Property_storable T> class property;
template<Property_storable T> class property_binding;

class untyped_property_data;
class untyped_property_binding;
class property_observer;
class binding_storage;

/**
 * @brief Thread safety utilities
 */
namespace threading {
    /// @brief Thread-local storage for binding evaluation state
    struct evaluation_context;
    
    /// @brief Get current thread's evaluation context
    [[nodiscard]] evaluation_context& current_context() noexcept;
    
    /// @brief RAII class for managing evaluation state
    class evaluation_guard {
    public:
        explicit evaluation_guard() noexcept;
        ~evaluation_guard() noexcept;
        
        MODERN_PROPERTY_DISABLE_COPY_MOVE(evaluation_guard)
        
    private:
        evaluation_context* previous_context_;
    };
}

} // namespace modern_property 