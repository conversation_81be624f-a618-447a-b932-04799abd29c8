#pragma once

#include "types.h"
#include "property_data.h"
#include "property_binding.h"
#include <unordered_map>
#include <memory>
#include <mutex>
#include <shared_mutex>

/**
 * @brief Modern Property System - Binding Storage
 * 
 * This file contains the binding storage system that manages
 * property bindings and ensures thread safety.
 */

namespace modern_property {

/**
 * @brief Thread-safe storage for property bindings
 * 
 * This class manages the storage and retrieval of property bindings
 * in a thread-safe manner using modern C++ concurrency primitives.
 */
class binding_storage {
public:
    /// @brief Map type for storing bindings
    using binding_map = std::unordered_map<const void*, std::unique_ptr<untyped_property_binding>>;

    /**
     * @brief Constructor
     */
    binding_storage() = default;
    
    /// @brief Destructor
    ~binding_storage() = default;
    
    MODERN_PROPERTY_DISABLE_COPY_MOVE(binding_storage)

    /**
     * @brief Store a binding for a property
     * @param property_id Unique identifier of the property
     * @param binding Binding to store (moved)
     * @return Previous binding (if any)
     */
    std::unique_ptr<untyped_property_binding> 
    store_binding(const void* property_id, std::unique_ptr<untyped_property_binding> binding) {
        std::unique_lock lock{mutex_};
        
        auto it = bindings_.find(property_id);
        std::unique_ptr<untyped_property_binding> old_binding;
        
        if (it != bindings_.end()) {
            old_binding = std::move(it->second);
        }
        
        if (binding) {
            bindings_[property_id] = std::move(binding);
        } else {
            bindings_.erase(property_id);
        }
        
        return old_binding;
    }
    
    /**
     * @brief Get a binding for a property
     * @param property_id Unique identifier of the property
     * @return Pointer to binding or nullptr if not found
     */
    [[nodiscard]] const untyped_property_binding* 
    get_binding(const void* property_id) const {
        std::shared_lock lock{mutex_};
        
        auto it = bindings_.find(property_id);
        return (it != bindings_.end()) ? it->second.get() : nullptr;
    }
    
    /**
     * @brief Remove a binding for a property
     * @param property_id Unique identifier of the property
     * @return Removed binding (if any)
     */
    std::unique_ptr<untyped_property_binding> 
    remove_binding(const void* property_id) {
        std::unique_lock lock{mutex_};
        
        auto it = bindings_.find(property_id);
        if (it != bindings_.end()) {
            auto binding = std::move(it->second);
            bindings_.erase(it);
            return binding;
        }
        
        return nullptr;
    }
    
    /**
     * @brief Check if a property has a binding
     * @param property_id Unique identifier of the property
     * @return true if binding exists
     */
    [[nodiscard]] bool has_binding(const void* property_id) const {
        std::shared_lock lock{mutex_};
        return bindings_.find(property_id) != bindings_.end();
    }
    
    /**
     * @brief Clear all bindings
     */
    void clear() {
        std::unique_lock lock{mutex_};
        bindings_.clear();
    }
    
    /**
     * @brief Get number of stored bindings
     * @return Binding count
     */
    [[nodiscard]] size_type size() const {
        std::shared_lock lock{mutex_};
        return static_cast<size_type>(bindings_.size());
    }
    
    /**
     * @brief Check if storage is empty
     * @return true if no bindings stored
     */
    [[nodiscard]] bool empty() const {
        std::shared_lock lock{mutex_};
        return bindings_.empty();
    }

private:
    mutable std::shared_mutex mutex_;
    binding_map bindings_;
};

/**
 * @brief Global binding storage singleton
 * @return Reference to global binding storage
 */
binding_storage& global_binding_storage();

} // namespace modern_property 