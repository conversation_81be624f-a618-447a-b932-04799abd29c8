#pragma once
#ifndef VARIANT_H
#define VARIANT_H

#include <any>

#include "variant_marco.h"
#include "typecast.h"

template<typename T>
T any_convert(const std::any& any);

class Variant {
public:
    Variant() = default;

    template<typename T, typename = std::enable_if_t<
                             !std::is_same_v<std::decay_t<T>, Variant>>>
    Variant(T&& value) : m_data(std::forward<T>(value)) {}

    template<typename T>
    Variant(std::initializer_list<T> args) {
        switch (args.size()) {
        case 0:
            break;
        case 1:
            m_data = std::move(args.begin());
            break;
        default:
            std::vector<T> vec(args.begin(), args.end());
            m_data = std::move(vec);
        }
    }
    Variant(std::initializer_list<Variant> args) {
        // Check if all items are pairs (for map construction)
        bool paired = true;
        for (const auto& item : args) {
            if (item.size() != 2) {
                paired = false;
                break;
            }
        }
        if (paired) {  // map
            std::map<std::string, Variant> map;
            for (const auto& item : args) {
                const auto& pair = item.to<std::pair<std::string, Variant>>();
                map.insert(pair);
            }
            m_data = std::move(map);
        } else {
            std::vector<Variant> vec(args.begin(), args.end());
            m_data = std::move(vec);
        }
    }

    Variant(const Variant& other) : m_data(other.m_data) {}
    Variant(Variant&& other) noexcept : m_data(std::move(other.m_data)) {}

    // Comparison operators
    auto operator<=>(const Variant& other) const noexcept {
        return to<long long>() <=> other.to<long long>();
    }
    bool operator==(const Variant& other) const noexcept {
        if (m_data.type() != other.m_data.type())
            return false;
        if (isNumber())
            return to<long long>() == other.to<long long>();
        return to<std::string>() == other.to<std::string>();
    }
    bool operator!() const noexcept {
        return !isValid();
    }

    // Copy assignment operator
    Variant& operator=(const Variant& other) {
        m_data = other.m_data;
        return *this;
    }
    // Move assignment operator
    Variant& operator=(Variant&& other) noexcept {
        if (this != &other) {
            m_data = std::move(other.m_data);
        }
        return *this;
    }
    // Assignment operator
    template<typename T, typename = std::enable_if_t<
                             !std::is_same_v<std::decay_t<T>, Variant>>>
    Variant& operator=(T&& value) {
        m_data = std::forward<T>(value);
        return *this;
    }
    // Arithmetic operators
    Variant& operator+=(const Variant& other) {
        m_data = to<long double>() + other.to<long double>();
        return *this;
    }
    Variant& operator-=(const Variant& other) {
        m_data = to<long double>() - other.to<long double>();
        return *this;
    }
    Variant& operator*=(const Variant& other) {
        m_data = to<long double>() * other.to<long double>();
        return *this;
    }
    Variant& operator/=(const Variant& other) {
        if (other.to<long long>() == 0) {
            m_data = 0;
        }
        m_data = to<long double>() / other.to<long double>();
        return *this;
    }
    Variant& operator%=(const Variant& other) {
        if (other.to<long long>() == 0) {
            m_data = 0;
        }
        m_data = to<long long>() % other.to<long long>();
        return *this;
    }
    Variant operator^=(const Variant& other) {
        m_data = std::pow(to<long long>(), other.to<long long>());
        return *this;
    }

    // Increment/decrement operators
    Variant operator++() {
        m_data = to<long double>() + 1;
        return *this;
    }
    Variant operator--() {
        m_data = to<long double>() - 1;
        return *this;
    }
    Variant operator++(int) {
        Variant temp = *this;
        m_data = to<long double>() + 1;
        return temp;
    }
    Variant operator--(int) {
        Variant temp = *this;
        m_data = to<long double>() - 1;
        return temp;
    }

    // Binary operators
    Variant operator+(const Variant& other) {
        return Variant(to<long double>() + other.to<long double>());
    }
    Variant operator-(const Variant& other) {
        return Variant(to<long double>() - other.to<long double>());
    }
    Variant operator*(const Variant& other) {
        return Variant(to<long double>() * other.to<long double>());
    }
    Variant operator/(const Variant& other) {
        if (other.to<long long>() == 0) {
            return Variant(0);
        }
        return Variant(to<long double>() / other.to<long double>());
    }
    Variant operator%(const Variant& other) {
        if (other.to<long long>() == 0) {
            return Variant(0);
        }
        return Variant(to<long long>() % other.to<long long>());
    }
    Variant operator^(const Variant& other) {
        return Variant(std::pow(to<long long>(), other.to<long long>()));
    }

    // template <typename T>
    // auto&& value(T& self) {
    //     if (self.m_data.type() != typeid(T)) {
    //         std::cerr << "Variant: type mismatch: \""
    //                   << self.m_data.type().name() << "\" to \""
    //                   << typeid(T).name() << "\""
    //                   << std::endl;
    //     }
    //     try {
    //         return std::forward<T>(self).template any_convert<T>(m_data);
    //     } catch (const std::exception& e) {
    //         throw std::runtime_error(
    //             "Variant: Failed to cast to the requested type. "
    //             + std::string(e.what()));
    //     } catch (...) {
    //         std::cerr << "Variant: Failed to cast to the requested type."
    //                   << std::endl;
    //     }
    //     return T();
    // }

    template <typename T>
    T value() const {
        try {
            return std::any_cast<T>(m_data);
        } catch (const std::exception& e) {
            throw std::runtime_error(
                "Variant: Failed to cast to the requested type: "
                + std::string(e.what()));
        } catch (...) {
            std::cerr << "Variant: Failed to cast to the requested type."
                      << std::endl;
        }
        return T();
    }

    template <typename T>
    void setValue(T&& value) {
        m_data = std::forward<T>(value);
    }

    template <typename T>
    std::enable_if_t<!std::is_reference_v<T> && !std::is_pointer_v<T>, T>
    to() const {
#ifndef IGNORE_MISMATCH_WARNING
        if (m_data.type() != typeid(T)) {
            std::cerr << "[Variant::to] Conversion type mismatch: \""
                      << m_data.type().name() << "\" to \""
                      << typeid(T).name() << "\""
                      << std::endl;
        }
#endif
        try {
            return any_convert<T>(m_data);
        } catch (const std::exception& e) {
            std::cerr << e.what() << std::endl;
        } catch (...) {
            std::cerr << "Variant: Failed to convert to the requested type."
                      << std::endl;
        }
        return T();
    }

    template <typename T>
    std::enable_if_t<std::is_reference_v<T>, T>
    to() {
        try {
            using BaseT = std::remove_reference_t<T>;
            if (m_data.type() == typeid(BaseT)) {
                return std::any_cast<BaseT&>(m_data);
            }
            std::cerr << "[Variant::to] Conversion reference type mismatch: \""
                      << m_data.type().name() << "\" => \""
                      << typeid(BaseT).name()
                      << std::endl;
            m_data = any_convert<BaseT>(m_data);
            return std::any_cast<BaseT&>(m_data);
        } catch (const std::exception& e) {
            std::cerr << "[Variant::to] Failed to cast to the requested type: "
                      << e.what() << std::endl;
        } catch (...) {
            std::cerr << "[Variant::to] Failed to cast to the requested type."
                      << std::endl;
        }
        throw std::runtime_error("[Variant::to] Conversion failure.");
    }

    template <typename T>
    std::enable_if_t<std::is_pointer_v<T>, T>
    to() {
        if (m_data.type() == typeid(T)) {
            return std::any_cast<T>(m_data);
        }
        std::cerr << "[Variant::to] Conversion pointer type mismatch: \""
                  << m_data.type().name() << "\" => \""
                  << typeid(T).name()
                  << std::endl;
        throw std::runtime_error("[Variant::to] Conversion failure.");
    }

    template<typename T, typename = std::enable_if_t<std::is_arithmetic_v<T>
                                                     || is_string_v<T>
                                                     || is_container_v<T> >>
    operator T () { return to<T>(); }

    template<typename T, typename = std::enable_if_t<std::is_arithmetic_v<T>
                                                     || is_string_v<T>
                                                     || is_container_v<T> >>
    operator T () const { return to<T>(); }

    template <typename T>
    T* data() {
        // Returns nullptr if the type does not match.
        return std::any_cast<T>(&m_data);
    }

    bool isValid() const {
        return m_data.has_value();
    }

    bool isNull() const {
        return !m_data.has_value() || isNullptr();
    }

    bool isEmpty() const {
        if (!isValid()) {
            return true;
        }
        if (isString()) {
            return to<std::string>() == "";
        }
        if (isVector() || isMap() || isHash()) {
            return size() == 0;
        }
        return true;
    }

    void reset() {
        m_data.reset();
    }

    const std::type_info& type() const {
        return m_data.type();
    }

    template <typename T>
    bool is() const {
        return m_data.type() == typeid(T);
    }

    bool isNumber() const {
        if (m_data.type() == typeid(short)
            || m_data.type() == typeid(unsigned short)
            || m_data.type() == typeid(int)
            || m_data.type() == typeid(unsigned int)
            || m_data.type() == typeid(long)
            || m_data.type() == typeid(unsigned long)
            || m_data.type() == typeid(long long)
            || m_data.type() == typeid(unsigned long long)
            || m_data.type() == typeid(float)
            || m_data.type() == typeid(double)
            || m_data.type() == typeid(long double)) {
            return true;
        }
        return false;
    }

    bool isString() const {
        return m_data.type() == typeid(const char*)
               || m_data.type() == typeid(std::string);
    }

    bool isNullptr() const {
        std::string type_name = type().name();
        return type_name.starts_with("std::nullptr_t");
    }

    bool isPointer() const {
        std::string type_name = type().name();
        return type_name.ends_with("* __ptr64");
    }

    bool isSharedPtr() const {
        std::string type_name = type().name();
        return type_name.starts_with("class std::shared_ptr<");
    }

    bool isVector() const {
        std::string type_name = type().name();
        return type_name.starts_with("class std::vector<");
    }

    bool isMap() const {
        std::string type_name = type().name();
        return type_name.starts_with("class std::map<");
    }

    bool isHash() const {
        std::string type_name = type().name();
        return type_name.starts_with("class std::unordered_map<");
    }

    std::string view() const {
        return std::string(to<View>());
    }

    std::string print() const {
        if (!isValid())
            return std::string("Variant(") + _typename() + ")";
        if (isNullptr())
            return "Variant(nullptr)";
        return std::string("Variant(") + _typename() + " " + view() + ")";
    }

    size_t size() const {
        try {
            const auto v = any_convert<std::vector<Variant>>(m_data);
            return v.size();
        } catch (const std::exception& e) {
            std::cerr << "Variant::size: Error: " << e.what() << std::endl;
        } catch (...) {
            std::cerr << "Variant::size: Unkown Error." << std::endl;
        }
        return isValid() ? 1 : 0;
    }

private:
    const char* _typename() const {
        if (m_data.type() == typeid(const char*))
            return "const char*";
        if (isString())
            return "std::string";
        if (isVector())
            return "std::vector";
        if (isMap())
            return "std::map";
        if (isHash())
            return "std::unordered_map";
        if (isSharedPtr())
            return "std::shared_ptr";
        return type().name();
    }
    std::any m_data;
};

template<typename T>
T any_convert(const std::any& any) {
    if (!any.has_value()) {
        if constexpr (std::is_reference_v<T>) {
            throw std::runtime_error("Cannot convert void any to reference.");
        } else {
            return T();
        }
    }

    if (any.type() == typeid(T)) {
        return std::any_cast<T>(any);
    }

    // #################### NUMERIC ####################
    if constexpr (std::is_arithmetic_v<T>) {
        APPLY_CONVERT(numeric_cast);
    }

    // #################### STRING ####################
    if constexpr (is_string_v<T>) {
        APPLY_CONVERT(string_cast);
    }
    if constexpr (std::is_same_v<T, View>) {
        APPLY_CONVERT(view_cast);
    }
    if constexpr (std::is_same_v<T, Id>) {
        APPLY_CONVERT(id_cast);
    }

    // #################### CONTAINER ####################
    if constexpr (is_vector_v<T>) {
        APPLY_CONVERT(vector_cast);
    }
    if constexpr (is_pair_v<T>) {
        APPLY_CONVERT(pair_cast);
    }
    if constexpr (is_map_v<T>) {
        APPLY_CONVERT(map_cast);
    }

    // #################### UNKOWN ####################
    throw std::runtime_error(
        std::string("Warning: Cannot convert from ")
        + any.type().name() + " to "
        + typeid(T).name() + "."
        );
}

template <typename T>
struct is_variant : std::false_type {};

template <>
struct is_variant<Variant> : std::true_type {};

template<class T>
typename std::enable_if<is_variant<T>::value, std::ostream&>::type
operator<<(std::ostream& os, T const & t)
{
    os << t.print();
    return os;
}

#endif // VARIANT_H
