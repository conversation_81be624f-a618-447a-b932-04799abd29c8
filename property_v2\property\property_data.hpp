#pragma once

/**
 * @file property_data.hpp
 * @brief 属性数据基础类
 * 
 * 定义属性数据的基础结构和接口。
 * 
 * <AUTHOR> System V2
 * @date 2025
 */

#include <utility>
#include "../core/concepts.hpp"
#include "../core/type_traits.hpp"
#include "../binding/binding_base.hpp"

namespace property_v2::property {

/**
 * @brief 属性数据模板类
 * 
 * 封装属性的值存储和基本访问操作，提供类型安全的值管理。
 * 
 * @tparam T 属性值类型
 */
template<typename T>
class property_data : public binding::property_data<T> {
public:
    using value_type = T;
    using parameter_type = core::parameter_type_t<T>;
    using rvalue_ref = core::rvalue_reference_type_t<T>;
    using arrow_operator_result = core::arrow_operator_result_t<T>;
    
    /**
     * @brief 默认构造函数
     */
    property_data() = default;
    
    /**
     * @brief 值构造函数
     * @param initial_value 初始值
     */
    explicit property_data(parameter_type initial_value) 
        : binding::property_data<T>(initial_value) {}
    
    /**
     * @brief 右值构造函数
     * @param initial_value 初始值（右值）
     */
    explicit property_data(rvalue_ref initial_value) 
        : binding::property_data<T>(std::move(initial_value)) {}
    
    /**
     * @brief 拷贝构造函数
     */
    property_data(const property_data&) = default;
    
    /**
     * @brief 移动构造函数
     */
    property_data(property_data&&) = default;
    
    /**
     * @brief 拷贝赋值运算符
     */
    property_data& operator=(const property_data&) = default;
    
    /**
     * @brief 移动赋值运算符
     */
    property_data& operator=(property_data&&) = default;
    
    /**
     * @brief 析构函数
     */
    ~property_data() = default;
    
    /**
     * @brief 获取值（绕过绑定）
     * 
     * 直接访问存储的值，不触发绑定求值或依赖注册。
     * 主要用于内部实现和调试。
     * 
     * @return 当前存储的值
     */
    parameter_type value_bypassing_bindings() const noexcept {
        return this->value_;
    }
    
    /**
     * @brief 设置值（绕过绑定）
     * 
     * 直接设置存储的值，不触发通知或绑定更新。
     * 主要用于绑定求值时的内部更新。
     * 
     * @param new_value 新值
     */
    void set_value_bypassing_bindings(parameter_type new_value) {
        this->value_ = new_value;
    }
    
    /**
     * @brief 设置值（绕过绑定，右值版本）
     * 
     * @param new_value 新值（右值）
     */
    void set_value_bypassing_bindings(rvalue_ref new_value) {
        this->value_ = std::move(new_value);
    }
    
    /**
     * @brief 箭头运算符结果
     * 
     * 为智能指针类型提供箭头运算符支持。
     * 
     * @return 箭头运算符的结果类型
     */
    arrow_operator_result operator->() const {
        if constexpr (core::Dereferenceable<T>) {
            return value_bypassing_bindings();
        } else if constexpr (core::Pointer<T>) {
            value_bypassing_bindings();  // 触发依赖注册
            return this->value_;
        } else {
            static_assert(core::Dereferenceable<T> || core::Pointer<T>, 
                         "Arrow operator is only available for dereferenceable or pointer types");
        }
    }
    
    /**
     * @brief 解引用运算符
     * 
     * @return 解引用的值
     */
    parameter_type operator*() const {
        return value_bypassing_bindings();
    }
    
    /**
     * @brief 检查值是否相等
     * 
     * 用于优化，避免不必要的通知。
     * 
     * @param other_value 要比较的值
     * @return 是否相等
     */
    bool is_equal(parameter_type other_value) const noexcept {
        if constexpr (core::Equality_comparable<T>) {
            return this->value_ == other_value;
        } else {
            return false;  // 如果不支持比较，总是认为不相等
        }
    }
    
    /**
     * @brief 检查值是否相等（右值版本）
     * 
     * @param other_value 要比较的值（右值）
     * @return 是否相等
     */
    bool is_equal(rvalue_ref other_value) const noexcept {
        if constexpr (core::Equality_comparable<T>) {
            return this->value_ == other_value;
        } else {
            return false;
        }
    }
    
    /**
     * @brief 交换两个属性数据的值
     * 
     * @param other 另一个属性数据对象
     */
    void swap(property_data& other) noexcept(std::is_nothrow_swappable_v<T>) {
        using std::swap;
        swap(this->value_, other.value_);
    }
    
    /**
     * @brief 获取值的哈希
     * 
     * @return 值的哈希值
     */
    std::size_t hash() const noexcept {
        if constexpr (requires { std::hash<T>{}(this->value_); }) {
            return std::hash<T>{}(this->value_);
        } else {
            return 0;  // 如果不支持哈希，返回0
        }
    }
    
    /**
     * @brief 获取值的字符串表示
     * 
     * 主要用于调试和日志输出。
     * 
     * @return 值的字符串表示
     */
    std::string to_string() const {
        if constexpr (core::is_string_v<T>) {
            return std::string(this->value_);
        } else if constexpr (std::is_arithmetic_v<T>) {
            return std::to_string(this->value_);
        } else if constexpr (requires { std::to_string(this->value_); }) {
            return std::to_string(this->value_);
        } else {
            return std::string(this->type_name());
        }
    }
};

/**
 * @brief 属性数据的交换函数
 * 
 * @tparam T 属性值类型
 * @param lhs 第一个属性数据
 * @param rhs 第二个属性数据
 */
template<typename T>
void swap(property_data<T>& lhs, property_data<T>& rhs) 
    noexcept(noexcept(lhs.swap(rhs))) {
    lhs.swap(rhs);
}

/**
 * @brief 属性数据的比较运算符
 * 
 * @tparam T 属性值类型
 * @param lhs 第一个属性数据
 * @param rhs 第二个属性数据
 * @return 是否相等
 */
template<typename T>
    requires core::Equality_comparable<T>
bool operator==(const property_data<T>& lhs, const property_data<T>& rhs) noexcept {
    return lhs.value_bypassing_bindings() == rhs.value_bypassing_bindings();
}

/**
 * @brief 属性数据与值的比较运算符
 * 
 * @tparam T 属性值类型
 * @param lhs 属性数据
 * @param rhs 值
 * @return 是否相等
 */
template<typename T>
    requires core::Equality_comparable<T>
bool operator==(const property_data<T>& lhs, core::parameter_type_t<T> rhs) noexcept {
    return lhs.is_equal(rhs);
}

/**
 * @brief 值与属性数据的比较运算符
 * 
 * @tparam T 属性值类型
 * @param lhs 值
 * @param rhs 属性数据
 * @return 是否相等
 */
template<typename T>
    requires core::Equality_comparable<T>
bool operator==(core::parameter_type_t<T> lhs, const property_data<T>& rhs) noexcept {
    return rhs.is_equal(lhs);
}

} // namespace property_v2::property
