#pragma once

/**
 * @file property_binding.hpp
 * @brief 属性绑定的核心实现
 * 
 * 实现属性绑定的创建、管理和求值机制。
 * 
 * <AUTHOR> System V2
 * @date 2025
 */

#include <memory>
#include <functional>
#include <vector>
#include <atomic>
#include <mutex>
#include "binding_base.hpp"
#include "../core/concepts.hpp"
#include "../core/memory.hpp"

namespace property_v2::binding {

// 前向声明
class binding_data;
template<typename T> class property_binding;

/**
 * @brief 绑定私有实现类
 */
class binding_private : public core::ref_counted {
public:
    /**
     * @brief 构造函数
     * @param vtable 函数虚表
     * @param location 源位置
     */
    binding_private(const binding_function_vtable* vtable, 
                   const core::source_location& location) noexcept;
    
    /**
     * @brief 析构函数
     */
    ~binding_private() override;
    
    /**
     * @brief 禁用拷贝
     */
    PROPERTY_DISABLE_COPY(binding_private)
    
    /**
     * @brief 设置属性数据指针
     */
    void set_property_data(untyped_property_data* data) noexcept { property_data_ = data; }
    
    /**
     * @brief 获取属性数据指针
     */
    untyped_property_data* get_property_data() const noexcept { return property_data_; }
    
    /**
     * @brief 求值绑定
     * @return 是否发生了值变更
     */
    bool evaluate();
    
    /**
     * @brief 检查是否正在更新
     */
    bool is_updating() const noexcept { return updating_.load(std::memory_order_relaxed); }
    
    /**
     * @brief 获取绑定错误
     */
    const binding_error& get_error() const noexcept { return error_; }
    
    /**
     * @brief 设置绑定错误
     */
    void set_error(binding_error error) noexcept { error_ = std::move(error); }
    
    /**
     * @brief 清除依赖观察者
     */
    void clear_dependency_observers();
    
    /**
     * @brief 获取源位置
     */
    const core::source_location& get_source_location() const noexcept { return location_; }
    
    /**
     * @brief 获取函数对象指针
     */
    void* get_functor() const noexcept;
    
    /**
     * @brief 标记为需要通知
     */
    void mark_dirty() noexcept { needs_notification_.store(true, std::memory_order_relaxed); }
    
    /**
     * @brief 检查是否需要通知
     */
    bool needs_notification() const noexcept { 
        return needs_notification_.load(std::memory_order_relaxed); 
    }
    
    /**
     * @brief 清除通知标记
     */
    void clear_notification_flag() noexcept { 
        needs_notification_.store(false, std::memory_order_relaxed); 
    }

private:
    const binding_function_vtable* vtable_;         ///< 函数虚表
    core::source_location location_;               ///< 源位置
    untyped_property_data* property_data_;         ///< 属性数据指针
    binding_error error_;                          ///< 绑定错误
    std::atomic<bool> updating_{false};           ///< 是否正在更新
    std::atomic<bool> needs_notification_{false}; ///< 是否需要通知
    
    /**
     * @brief 获取函数对象存储位置
     */
    std::byte* get_functor_storage() const noexcept;
    
    /**
     * @brief 计算对象大小（包含函数对象）
     */
    static std::size_t calculate_size(const binding_function_vtable* vtable) noexcept;
};

/**
 * @brief 未类型化属性绑定
 */
class untyped_property_binding {
public:
    /**
     * @brief 默认构造函数，创建空绑定
     */
    untyped_property_binding() noexcept = default;
    
    /**
     * @brief 从绑定私有对象构造
     */
    explicit untyped_property_binding(core::intrusive_ptr<binding_private> impl) noexcept
        : impl_(std::move(impl)) {}
    
    /**
     * @brief 拷贝构造函数
     */
    untyped_property_binding(const untyped_property_binding&) = default;
    
    /**
     * @brief 移动构造函数
     */
    untyped_property_binding(untyped_property_binding&&) = default;
    
    /**
     * @brief 拷贝赋值运算符
     */
    untyped_property_binding& operator=(const untyped_property_binding&) = default;
    
    /**
     * @brief 移动赋值运算符
     */
    untyped_property_binding& operator=(untyped_property_binding&&) = default;
    
    /**
     * @brief 析构函数
     */
    ~untyped_property_binding() = default;
    
    /**
     * @brief 检查绑定是否为空
     */
    bool is_null() const noexcept { return !impl_; }
    
    /**
     * @brief 获取绑定错误
     */
    binding_error error() const noexcept {
        return impl_ ? impl_->get_error() : binding_error{};
    }
    
    /**
     * @brief 获取源位置
     */
    core::source_location source_location() const noexcept {
        return impl_ ? impl_->get_source_location() : core::source_location{};
    }
    
    /**
     * @brief 获取内部实现指针
     */
    core::intrusive_ptr<binding_private> get_impl() const noexcept { return impl_; }

protected:
    core::intrusive_ptr<binding_private> impl_;  ///< 内部实现
};

/**
 * @brief 类型化属性绑定
 * @tparam T 属性值类型
 */
template<typename T>
class property_binding : public untyped_property_binding {
public:
    using value_type = T;
    
    /**
     * @brief 默认构造函数
     */
    property_binding() = default;
    
    /**
     * @brief 从函数对象构造绑定
     * @tparam F 函数对象类型
     * @param functor 函数对象
     * @param location 源位置
     */
    template<typename F>
        requires core::Nullary_invocable_r<F, T>
    property_binding(F&& functor, 
                    const core::source_location& location = PROPERTY_DEFAULT_BINDING_LOCATION)
        : untyped_property_binding(create_binding_impl(std::forward<F>(functor), location)) {}
    
    /**
     * @brief 从未类型化绑定构造
     */
    explicit property_binding(const untyped_property_binding& other) noexcept
        : untyped_property_binding(other) {}

private:
    /**
     * @brief 创建绑定实现
     * @tparam F 函数对象类型
     * @param functor 函数对象
     * @param location 源位置
     */
    template<typename F>
    static core::intrusive_ptr<binding_private> create_binding_impl(
        F&& functor, const core::source_location& location) {
        
        using functor_type = std::remove_cvref_t<F>;
        static constexpr auto vtable = binding_function_vtable::create_for<functor_type, T>();
        
        // 计算所需内存大小
        constexpr std::size_t base_size = sizeof(binding_private);
        constexpr std::size_t alignment = alignof(std::max_align_t);
        constexpr std::size_t aligned_base_size = (base_size + alignment - 1) & ~(alignment - 1);
        constexpr std::size_t total_size = aligned_base_size + sizeof(functor_type);
        
        // 分配内存
        auto* memory = new std::byte[total_size];
        
        // 构造绑定对象
        auto* binding = new (memory) binding_private(&vtable, location);
        
        // 构造函数对象
        auto* functor_storage = memory + aligned_base_size;
        new (functor_storage) functor_type(std::forward<F>(functor));
        
        return core::intrusive_ptr<binding_private>(binding);
    }
};

/**
 * @brief 创建属性绑定的便利函数
 * @tparam F 函数对象类型
 * @param functor 函数对象
 * @param location 源位置
 * @return 属性绑定对象
 */
template<typename F>
    requires core::Nullary_invocable<F>
auto make_property_binding(F&& functor, 
                          const core::source_location& location = PROPERTY_DEFAULT_BINDING_LOCATION) {
    using return_type = std::invoke_result_t<F>;
    return property_binding<return_type>(std::forward<F>(functor), location);
}

/**
 * @brief 从另一个属性创建绑定的便利函数
 * @tparam PropertyType 属性类型
 * @param other_property 其他属性
 * @param location 源位置
 * @return 属性绑定对象
 */
template<typename PropertyType>
property_binding<PropertyType> make_property_binding(
    const PropertyType& other_property,
    const core::source_location& location = PROPERTY_DEFAULT_BINDING_LOCATION) {
    return make_property_binding([&other_property]() -> PropertyType { 
        return other_property; 
    }, location);
}

} // namespace property_v2::binding
