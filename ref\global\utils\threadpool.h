﻿#ifndef THREADPOOL_H
#define THREADPOOL_H

#include <vector>
#include <queue>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <functional>
#include <future>
#include <atomic>
#include <iostream>

/**
 * @class ThreadPool
 * @brief A thread pool for executing tasks asynchronously
 *
 * This class provides a pool of worker threads that can execute tasks
 * asynchronously. It is used by the event system to execute event handlers
 * without creating a new thread for each handler.
 */
class ThreadPool {
public:
    /**
     * @brief Get the singleton instance of the thread pool
     *
     * @return Reference to the singleton instance
     */
    static ThreadPool& instance() {
        static ThreadPool instance;
        return instance;
    }

    /**
     * @brief Enqueue a task for execution
     *
     * @param task The task to execute
     */
    void enqueue(std::function<void()> task) {
        {
            std::unique_lock<std::mutex> lock(m_mutex);

            // Don't allow enqueueing after stopping the pool
            if (m_stop) {
                throw std::runtime_error("Enqueue on stopped ThreadPool");
            }

            m_tasks.emplace(std::move(task));
        }
        m_condition.notify_one();
    }

    /**
     * @brief Get the number of active threads
     *
     * @return The number of active threads
     */
    size_t activeThreads() const {
        return m_activeThreads.load();
    }

    /**
     * @brief Get the number of queued tasks
     *
     * @return The number of queued tasks
     */
    size_t queuedTasks() const {
        std::unique_lock<std::mutex> lock(m_mutex);
        return m_tasks.size();
    }

    /**
     * @brief Set the number of threads in the pool
     *
     * @param numThreads The number of threads
     */
    void setThreadCount(size_t numThreads) {
        std::unique_lock<std::mutex> lock(m_mutex);

        // Don't allow changing thread count after stopping
        if (m_stop) {
            return;
        }

        // If increasing thread count
        if (numThreads > m_workers.size()) {
            // Add new workers
            for (size_t i = m_workers.size(); i < numThreads; ++i) {
                m_workers.emplace_back(&ThreadPool::workerThread, this);
            }
        }
        // If decreasing thread count
        else if (numThreads < m_workers.size()) {
            // Set the number of threads to stop
            m_threadsToStop = m_workers.size() - numThreads;

            // Notify all threads to check if they should stop
            m_condition.notify_all();

            // Wait for threads to stop
            while (m_threadsToStop > 0) {
                m_stoppedCondition.wait(lock);
            }

            // Remove stopped threads
            for (auto it = m_workers.begin(); it != m_workers.end();) {
                if (!it->joinable()) {
                    it = m_workers.erase(it);
                } else {
                    ++it;
                }
            }
        }
    }

private:
    // Private constructor for singleton
    ThreadPool(size_t numThreads = std::thread::hardware_concurrency())
        : m_stop(false), m_threadsToStop(0), m_activeThreads(0) {
        for (size_t i = 0; i < numThreads; ++i) {
            m_workers.emplace_back(&ThreadPool::workerThread, this);
        }
    }

    // Destructor
    ~ThreadPool() {
        {
            std::unique_lock<std::mutex> lock(m_mutex);
            m_stop = true;
        }

        m_condition.notify_all();

        for (auto& worker : m_workers) {
            if (worker.joinable()) {
                worker.join();
            }
        }
    }

    // Worker thread function
    void workerThread() {
        while (true) {
            std::function<void()> task;

            {
                std::unique_lock<std::mutex> lock(m_mutex);

                // Wait for a task or stop signal
                m_condition.wait(lock, [this] {
                    return m_stop || !m_tasks.empty() || m_threadsToStop > 0;
                });

                // Check if this thread should stop
                if (m_threadsToStop > 0) {
                    --m_threadsToStop;
                    m_stoppedCondition.notify_one();
                    return;
                }

                // Check if the pool is stopping and there are no more tasks
                if (m_stop && m_tasks.empty()) {
                    return;
                }

                // Get the next task
                if (!m_tasks.empty()) {
                    task = std::move(m_tasks.front());
                    m_tasks.pop();
                }
            }

            // Execute the task
            if (task) {
                m_activeThreads++;

                try {
                    task();
                } catch (const std::exception& e) {
                    std::cerr << "Exception in thread pool task: " << e.what() << std::endl;
                } catch (...) {
                    std::cerr << "Unknown exception in thread pool task" << std::endl;
                }

                m_activeThreads--;
            }
        }
    }

    // Thread pool state
    std::vector<std::thread> m_workers;
    std::queue<std::function<void()>> m_tasks;

    // Synchronization
    mutable std::mutex m_mutex;
    std::condition_variable m_condition;
    std::condition_variable m_stoppedCondition;
    bool m_stop;

    // Thread management
    size_t m_threadsToStop;
    std::atomic<size_t> m_activeThreads;
};

#endif // THREADPOOL_H
