#ifndef GLOBAL_H
#define GLOBAL_H

#include "typetraits_qt.h"
#include <string>

using str_t     = std::string;
using cstr_t    = const char*;
using strv_t    = std::string_view;

#if (defined(__GNUC__) || defined(__clang__)) && !defined(_MSC_VER)
# define ALWAYS_INLINE inline __attribute__((always_inline))
#elif defined(_MSC_VER)
# define ALWAYS_INLINE __forceinline
#else
# define ALWAYS_INLINE inline
#endif

//qtbase/src/corelib/global/qtclasshelpermacros.h
#define DISABLE_COPY(Class) \
Class(const Class&) = delete;\
    Class& operator=(const Class&) = delete;

#define DISABLE_COPY_MOVE(Class) \
DISABLE_COPY(Class) \
    Class(Class&&) = delete; \
    Class& operator=(Class&&) = delete;

//qtbase/src/corelib/global/qswap.h
template <typename T>
constexpr inline void ptr_swap(T* &lhs, T* &rhs) noexcept
{
    T *tmp = lhs;
    lhs = rhs;
    rhs = tmp;
}

#define ASSERT_X(cond, msg, describe) \
do { \
        if (!(cond)) { \
            std::cerr << (msg) << ": " << (describe) << "\n"; \
            assert(cond); \
    } \
} while (false)

template <typename T>
constexpr inline const T &Min(const T &a, const T &b) { return (a < b) ? a : b; }
template <typename T>
constexpr inline const T &Max(const T &a, const T &b) { return (a < b) ? b : a; }
template <typename T>
constexpr inline const T &Bound(const T &min, const T &val, const T &max)
{
    assert(!(max < min));
    return Max(min, Min(max, val));
}

template <typename T, typename U>
constexpr inline TypeTraits::Promoted<T, U> Min(const T &a, const U &b)
{
    using P = TypeTraits::Promoted<T, U>;
    P _a = a;
    P _b = b;
    return (_a < _b) ? _a : _b;
}
template <typename T, typename U>
constexpr inline TypeTraits::Promoted<T, U> Max(const T &a, const U &b)
{
    using P = TypeTraits::Promoted<T, U>;
    P _a = a;
    P _b = b;
    return (_a < _b) ? _b : _a;
}
template <typename T, typename U>
constexpr inline TypeTraits::Promoted<T, U> Bound(const T &min, const U &val, const T &max)
{
    assert(!(max < min));
    return Max(min, Min(max, val));
}
template <typename T, typename U>
constexpr inline TypeTraits::Promoted<T, U> Bound(const T &min, const T &val, const U &max)
{
    using P = TypeTraits::Promoted<T, U>;
    assert(!(P(max) < P(min)));
    return Max(min, Min(max, val));
}
template <typename T, typename U>
constexpr inline TypeTraits::Promoted<T, U> Bound(const U &min, const T &val, const T &max)
{
    using P = TypeTraits::Promoted<T, U>;
    assert(!(P(max) < P(min)));
    return Max(min, Min(max, val));
}

#endif // GLOBAL_H
