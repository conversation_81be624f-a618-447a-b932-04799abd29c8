#pragma once

/**
 * @file memory.hpp
 * @brief 现代化属性系统的内存管理工具
 * 
 * 提供智能指针、内存池和RAII资源管理工具。
 * 
 * <AUTHOR> System V2
 * @date 2025
 */

#include <memory>
#include <atomic>
#include <utility>
#include <type_traits>
#include "concepts.hpp"

namespace property_v2::core {

/**
 * @brief 引用计数基类
 * 
 * 提供线程安全的引用计数功能，用于实现智能指针。
 */
class ref_counted {
public:
    /**
     * @brief 默认构造函数，引用计数初始化为0
     */
    ref_counted() noexcept : ref_count_(0) {}
    
    /**
     * @brief 拷贝构造函数，新对象引用计数初始化为0
     */
    ref_counted(const ref_counted&) noexcept : ref_count_(0) {}
    
    /**
     * @brief 赋值运算符，不影响引用计数
     */
    ref_counted& operator=(const ref_counted&) noexcept { return *this; }
    
    /**
     * @brief 虚析构函数
     */
    virtual ~ref_counted() = default;
    
    /**
     * @brief 增加引用计数
     */
    void add_ref() const noexcept {
        ref_count_.fetch_add(1, std::memory_order_relaxed);
    }
    
    /**
     * @brief 减少引用计数
     * @return 如果引用计数变为0则返回false，否则返回true
     */
    bool release() const noexcept {
        return ref_count_.fetch_sub(1, std::memory_order_acq_rel) != 1;
    }
    
    /**
     * @brief 获取当前引用计数
     * @return 当前引用计数值
     */
    std::size_t use_count() const noexcept {
        return ref_count_.load(std::memory_order_relaxed);
    }

private:
    mutable std::atomic<std::size_t> ref_count_;
};

/**
 * @brief 侵入式智能指针
 * 
 * 类似于std::shared_ptr，但要求被管理的对象继承自ref_counted。
 * 相比std::shared_ptr，避免了额外的控制块分配。
 * 
 * @tparam T 被管理的对象类型，必须继承自ref_counted
 */
template<typename T>
    requires std::derived_from<T, ref_counted>
class intrusive_ptr {
public:
    using element_type = T;
    using pointer = T*;
    using reference = T&;
    
    /**
     * @brief 默认构造函数，创建空指针
     */
    constexpr intrusive_ptr() noexcept : ptr_(nullptr) {}
    
    /**
     * @brief 空指针构造函数
     */
    constexpr intrusive_ptr(std::nullptr_t) noexcept : ptr_(nullptr) {}
    
    /**
     * @brief 从原始指针构造
     * @param ptr 原始指针，会增加其引用计数
     */
    explicit intrusive_ptr(T* ptr) noexcept : ptr_(ptr) {
        if (ptr_) {
            ptr_->add_ref();
        }
    }
    
    /**
     * @brief 拷贝构造函数
     */
    intrusive_ptr(const intrusive_ptr& other) noexcept : ptr_(other.ptr_) {
        if (ptr_) {
            ptr_->add_ref();
        }
    }
    
    /**
     * @brief 移动构造函数
     */
    intrusive_ptr(intrusive_ptr&& other) noexcept : ptr_(std::exchange(other.ptr_, nullptr)) {}
    
    /**
     * @brief 析构函数，减少引用计数
     */
    ~intrusive_ptr() {
        if (ptr_ && !ptr_->release()) {
            delete ptr_;
        }
    }
    
    /**
     * @brief 拷贝赋值运算符
     */
    intrusive_ptr& operator=(const intrusive_ptr& other) noexcept {
        if (this != &other) {
            reset(other.ptr_);
        }
        return *this;
    }
    
    /**
     * @brief 移动赋值运算符
     */
    intrusive_ptr& operator=(intrusive_ptr&& other) noexcept {
        if (this != &other) {
            reset();
            ptr_ = std::exchange(other.ptr_, nullptr);
        }
        return *this;
    }
    
    /**
     * @brief 重置指针
     * @param ptr 新的指针值
     */
    void reset(T* ptr = nullptr) noexcept {
        if (ptr != ptr_) {
            if (ptr) {
                ptr->add_ref();
            }
            T* old_ptr = std::exchange(ptr_, ptr);
            if (old_ptr && !old_ptr->release()) {
                delete old_ptr;
            }
        }
    }
    
    /**
     * @brief 释放指针所有权
     * @return 原始指针
     */
    T* release() noexcept {
        return std::exchange(ptr_, nullptr);
    }
    
    /**
     * @brief 获取原始指针
     */
    T* get() const noexcept { return ptr_; }
    
    /**
     * @brief 解引用运算符
     */
    T& operator*() const noexcept { return *ptr_; }
    
    /**
     * @brief 箭头运算符
     */
    T* operator->() const noexcept { return ptr_; }
    
    /**
     * @brief 布尔转换运算符
     */
    explicit operator bool() const noexcept { return ptr_ != nullptr; }
    
    /**
     * @brief 交换两个智能指针
     */
    void swap(intrusive_ptr& other) noexcept {
        std::swap(ptr_, other.ptr_);
    }
    
    /**
     * @brief 获取引用计数
     */
    std::size_t use_count() const noexcept {
        return ptr_ ? ptr_->use_count() : 0;
    }
    
    /**
     * @brief 比较运算符
     */
    friend bool operator==(const intrusive_ptr& lhs, const intrusive_ptr& rhs) noexcept {
        return lhs.ptr_ == rhs.ptr_;
    }
    
    friend bool operator!=(const intrusive_ptr& lhs, const intrusive_ptr& rhs) noexcept {
        return !(lhs == rhs);
    }
    
    friend bool operator<(const intrusive_ptr& lhs, const intrusive_ptr& rhs) noexcept {
        return std::less<T*>{}(lhs.ptr_, rhs.ptr_);
    }

private:
    T* ptr_;
};

/**
 * @brief 创建侵入式智能指针
 * @tparam T 对象类型
 * @tparam Args 构造参数类型
 * @param args 构造参数
 * @return 新创建的智能指针
 */
template<typename T, typename... Args>
    requires std::derived_from<T, ref_counted> && std::constructible_from<T, Args...>
intrusive_ptr<T> make_intrusive(Args&&... args) {
    return intrusive_ptr<T>(new T(std::forward<Args>(args)...));
}

/**
 * @brief RAII作用域守卫
 * 
 * 在作用域结束时自动执行清理函数。
 * 
 * @tparam F 清理函数类型
 */
template<typename F>
    requires std::invocable<F>
class scope_guard {
public:
    /**
     * @brief 构造函数
     * @param cleanup 清理函数
     */
    explicit scope_guard(F cleanup) noexcept : cleanup_(std::move(cleanup)), active_(true) {}
    
    /**
     * @brief 移动构造函数
     */
    scope_guard(scope_guard&& other) noexcept 
        : cleanup_(std::move(other.cleanup_)), active_(std::exchange(other.active_, false)) {}
    
    /**
     * @brief 禁用拷贝
     */
    scope_guard(const scope_guard&) = delete;
    scope_guard& operator=(const scope_guard&) = delete;
    scope_guard& operator=(scope_guard&&) = delete;
    
    /**
     * @brief 析构函数，执行清理
     */
    ~scope_guard() {
        if (active_) {
            cleanup_();
        }
    }
    
    /**
     * @brief 取消清理
     */
    void dismiss() noexcept {
        active_ = false;
    }

private:
    F cleanup_;
    bool active_;
};

/**
 * @brief 创建作用域守卫
 * @tparam F 清理函数类型
 * @param cleanup 清理函数
 * @return 作用域守卫对象
 */
template<typename F>
scope_guard<F> make_scope_guard(F&& cleanup) {
    return scope_guard<F>(std::forward<F>(cleanup));
}

} // namespace property_v2::core
