#pragma once

/**
 * @file binding_base.hpp
 * @brief 属性绑定系统的基础设施
 * 
 * 定义绑定系统的核心接口和基础类型。
 * 
 * <AUTHOR> System V2
 * @date 2025
 */

#include <functional>
#include <memory>
#include <string>
#include <variant>
#include <vector>
#include <atomic>
#include "../core/concepts.hpp"
#include "../core/type_traits.hpp"
#include "../core/memory.hpp"
#include "../core/utils.hpp"

namespace property_v2::binding {

// 前向声明
class untyped_property_data;
class binding_private;
class binding_evaluation_state;
template<typename T> class property_data;

/**
 * @brief 绑定错误类型枚举
 */
enum class binding_error_type {
    no_error,           ///< 无错误
    binding_loop,       ///< 绑定循环
    evaluation_error,   ///< 求值错误
    type_mismatch,      ///< 类型不匹配
    unknown_error       ///< 未知错误
};

/**
 * @brief 绑定错误信息
 */
class binding_error {
public:
    /**
     * @brief 默认构造函数，创建无错误状态
     */
    binding_error() noexcept : type_(binding_error_type::no_error) {}
    
    /**
     * @brief 构造函数
     * @param type 错误类型
     * @param description 错误描述
     */
    binding_error(binding_error_type type, std::string description = {}) noexcept
        : type_(type), description_(std::move(description)) {}
    
    /**
     * @brief 检查是否有错误
     */
    bool has_error() const noexcept { return type_ != binding_error_type::no_error; }
    
    /**
     * @brief 获取错误类型
     */
    binding_error_type type() const noexcept { return type_; }
    
    /**
     * @brief 获取错误描述
     */
    const std::string& description() const noexcept { return description_; }
    
    /**
     * @brief 清除错误
     */
    void clear() noexcept {
        type_ = binding_error_type::no_error;
        description_.clear();
    }

private:
    binding_error_type type_;
    std::string description_;
};

/**
 * @brief 绑定函数虚表
 * 
 * 提供类型擦除的函数调用接口，支持不同类型的可调用对象。
 */
struct binding_function_vtable {
    using call_fn = bool(*)(untyped_property_data*, void*);
    using destroy_fn = void(*)(void*);
    using move_construct_fn = void(*)(void*, void*);
    using copy_construct_fn = void(*)(void*, const void*);
    
    call_fn call;                       ///< 调用函数
    destroy_fn destroy;                 ///< 销毁函数
    move_construct_fn move_construct;   ///< 移动构造函数
    copy_construct_fn copy_construct;   ///< 拷贝构造函数
    std::size_t size;                   ///< 对象大小
    std::size_t alignment;              ///< 对象对齐
    
    /**
     * @brief 为特定类型创建虚表
     * @tparam Callable 可调用对象类型
     * @tparam PropertyType 属性类型
     */
    template<typename Callable, typename PropertyType>
    static constexpr binding_function_vtable create_for() noexcept {
        static_assert(alignof(Callable) <= alignof(std::max_align_t),
                     "Bindings do not support over-aligned functors!");
        
        return binding_function_vtable{
            // call
            [](untyped_property_data* data_ptr, void* functor_ptr) -> bool {
                if constexpr (core::Nullary_invocable<Callable>) {
                    auto* property_ptr = static_cast<property_data<PropertyType>*>(data_ptr);
                    auto* evaluator = static_cast<Callable*>(functor_ptr);
                    
                    PropertyType new_value = std::invoke(*evaluator);
                    
                    if constexpr (core::Equality_comparable<PropertyType>) {
                        if (new_value == property_ptr->value_bypassing_bindings()) {
                            return false;
                        }
                    }
                    
                    property_ptr->set_value_bypassing_bindings(std::move(new_value));
                    return true;
                } else {
                    static_assert(core::Nullary_invocable<Callable>, 
                                "Binding function must be callable without arguments");
                    return false;
                }
            },
            // destroy
            [](void* functor_ptr) {
                static_cast<Callable*>(functor_ptr)->~Callable();
            },
            // move_construct
            [](void* dest, void* src) {
                new (dest) Callable(std::move(*static_cast<Callable*>(src)));
            },
            // copy_construct
            [](void* dest, const void* src) {
                if constexpr (std::copy_constructible<Callable>) {
                    new (dest) Callable(*static_cast<const Callable*>(src));
                } else {
                    // 如果不支持拷贝，则抛出异常
                    throw std::runtime_error("Callable type does not support copy construction");
                }
            },
            sizeof(Callable),
            alignof(Callable)
        };
    }
};

/**
 * @brief 绑定函数包装器
 */
struct binding_function {
    const binding_function_vtable* vtable;  ///< 虚表指针
    void* functor;                          ///< 函数对象指针
    
    /**
     * @brief 调用绑定函数
     * @param data_ptr 属性数据指针
     * @return 是否发生了值变更
     */
    bool operator()(untyped_property_data* data_ptr) const {
        return vtable->call(data_ptr, functor);
    }
};

/**
 * @brief 未类型化属性数据基类
 */
class untyped_property_data {
public:
    virtual ~untyped_property_data() = default;
    
    /**
     * @brief 获取类型信息
     */
    virtual std::string_view type_name() const noexcept = 0;
    
    /**
     * @brief 获取类型哈希
     */
    virtual std::size_t type_hash() const noexcept = 0;
};

/**
 * @brief 类型化属性数据模板类
 * @tparam T 属性值类型
 */
template<typename T>
class property_data : public untyped_property_data {
public:
    using value_type = T;
    using parameter_type = core::parameter_type_t<T>;
    using rvalue_ref = core::rvalue_reference_type_t<T>;
    
    /**
     * @brief 默认构造函数
     */
    property_data() = default;
    
    /**
     * @brief 值构造函数
     */
    explicit property_data(parameter_type initial_value) : value_(initial_value) {}
    
    /**
     * @brief 右值构造函数
     */
    explicit property_data(rvalue_ref initial_value) : value_(std::move(initial_value)) {}
    
    /**
     * @brief 获取值（绕过绑定）
     */
    parameter_type value_bypassing_bindings() const noexcept { return value_; }
    
    /**
     * @brief 设置值（绕过绑定）
     */
    void set_value_bypassing_bindings(parameter_type new_value) {
        value_ = new_value;
    }
    
    /**
     * @brief 设置值（绕过绑定，右值版本）
     */
    void set_value_bypassing_bindings(rvalue_ref new_value) {
        value_ = std::move(new_value);
    }
    
    /**
     * @brief 获取类型名称
     */
    std::string_view type_name() const noexcept override {
        return typeid(T).name();
    }
    
    /**
     * @brief 获取类型哈希
     */
    std::size_t type_hash() const noexcept override {
        return typeid(T).hash_code();
    }

protected:
    mutable T value_{};  ///< 属性值
};

/**
 * @brief 绑定状态
 */
struct binding_status {
    binding_evaluation_state* currently_evaluating_binding = nullptr;  ///< 当前正在求值的绑定
    core::thread_id thread_id{};                                       ///< 线程ID
    std::atomic<bool> in_update_group{false};                          ///< 是否在更新组中
};

/**
 * @brief 获取当前线程的绑定状态
 */
binding_status& get_binding_status() noexcept;

/**
 * @brief 绑定求值状态
 */
class binding_evaluation_state {
public:
    /**
     * @brief 构造函数
     * @param binding 绑定对象
     */
    explicit binding_evaluation_state(binding_private* binding) noexcept;
    
    /**
     * @brief 析构函数
     */
    ~binding_evaluation_state() noexcept;
    
    /**
     * @brief 禁用拷贝和移动
     */
    PROPERTY_DISABLE_COPY_MOVE(binding_evaluation_state)
    
    /**
     * @brief 获取绑定对象
     */
    binding_private* get_binding() const noexcept { return binding_; }
    
    /**
     * @brief 添加依赖属性
     */
    void add_dependency(const untyped_property_data* property);
    
    /**
     * @brief 检查是否已经依赖某个属性
     */
    bool has_dependency(const untyped_property_data* property) const noexcept;

private:
    binding_private* binding_;
    binding_evaluation_state* previous_state_;
    std::vector<const untyped_property_data*> dependencies_;
};

} // namespace property_v2::binding
