﻿set(MODULE global)
set(MODULE_SRC
    # global
    global.h
    utils/uuid.h
    utils/uuid.cpp
    utils/threadpool.h

    types/id.h
    types/metatype.h
    types/typecast.h
    types/typetraits.h
    types/typetraits_qt.h
    types/variant.h
    types/variant_marco.h
    platform.h
    logger.h
    logging.h
    # settings.h
    # settings.cpp

    # base
    #base/typefunctions.h
    #base/type.h
    #base/type.cpp
    # base/metatype.h
    # base/variant.h
    # base/utils.h

    # base/property/property.h
    # base/property/propertyprivate.h
    # base/property/property_p.h
    # base/property/property.cpp
    # base/property/attribute.h
    # base/property/shareddata.h
    # base/property/taggedpointer.h
    # base/property/bindingstorage.h
    # base/property/varlengtharray.h

    # base/object/object.h
    # base/object/object.cpp
    # base/object/container.h
    # base/object/container.cpp
    # base/object/manager.h
    # base/object/manager.cpp
    # base/object/root.h
    # base/object/root.cpp
    # base/object/entity.h
    # base/object/entity.cpp
    # base/object/file.h
    # base/object/file.cpp
    # base/object/empty.h
    # base/object/empty.cpp
    # base/object/registry.h
    # base/object/registry.cpp

    # base/object/relation.h
    # base/object/relation.cpp

    # base/event/event.h
    # base/event/event.cpp
    # base/state/state.h
    # base/state/state.cpp

    # base/component/component.h
    # base/component/component.cpp
    # base/component/filesystemcomponent.h
    # base/component/filesystemcomponent.cpp

    # base/action/action.h
    # base/action/action.cpp
    # base/formula/formula.h
    # base/formula/token.h
    # base/formula/lexer.h
    # base/formula/ast.h
    # base/formula/parser.h
    # base/formula/environment.h

    # io
    # io/ifilesystem.h

    # database
    # database/idatabaseservice.h
    # database/database.h
    # database/database.cpp
    # database/databaseaccess.h
    # database/databaseaccess.cpp
    # database/databasemanager.h
    # database/databasemanager.cpp

    # filesystem
    # filesystem/ifilesystemservice.h
    # filesystem/fileutils.h
    # filesystem/fileutils.cpp
    # filesystem/casecorrectpath.h
    # filesystem/fileinfogatherer.h
    # filesystem/fileinfogatherer.cpp
    # filesystem/filesystemmanager.h
    # filesystem/filesystemmanager.cpp

    # module/module.h

    # modularity
    modularity/context.h
    modularity/imoduleinterface.h
    modularity/imodulesetup.h
    modularity/injectable.h
    modularity/ioc.h
    modularity/moduleinfo.h
    modularity/modulesioc.h
)

#include(${CMAKE_SOURCE_DIR}/build/cmake/module.cmake)
#add_library(${MODULE} STATIC ${MODULE_SRC})
add_library(${MODULE})

target_sources(${MODULE} PRIVATE
    #${ui_headers}
    #${RCC_SOURCES}
    #${RCC_BIG_SOURCES}
    ${MODULE_SRC}
    )

target_include_directories(${MODULE} PUBLIC
    ${PROJECT_BINARY_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}  # 支持通过子路径#include
    ${CMAKE_CURRENT_SOURCE_DIR}/types  # 支持通过子路径#include
    # ${CMAKE_CURRENT_SOURCE_DIR}/base/
    # ${CMAKE_CURRENT_SOURCE_DIR}/base/object/
    # ${CMAKE_CURRENT_SOURCE_DIR}/base/event/
    # ${CMAKE_CURRENT_SOURCE_DIR}/base/state/
    # ${CMAKE_CURRENT_SOURCE_DIR}/filesystem/
    # ${CMAKE_CURRENT_BINARY_DIR}
    # ${PROJECT_ROOT_DIR}
    # ${CMAKE_SOURCE_DIR}/src
    # ${PROJECT_ROOT_DIR}/src/framework
    # ${PROJECT_ROOT_DIR}/src/framework/global
    # ${MODULE_INCLUDE}
)

if (USE_SPDLOG)
    set(MODULE_LINK spdlog::spdlog)
endif(USE_SPDLOG)

add_subdirectory(io)

set(MODULE_LINK io ${MODULE_LINK})
# set(MODULE_LINK ${QT_LIBRARIES} ${MODULE_LINK})
# set(MODULE_LINK ${CMAKE_DL_LIBS} ${MODULE_LINK})

target_link_libraries(${MODULE} PUBLIC ${MODULE_LINK})


# include_directories(${CMAKE_SOURCE_DIR}/imports/sqlite)
#add_library(SQLite3 STATIC ${CMAKE_SOURCE_DIR}/imports/sqlite/sqlite3.c)
#target_link_libraries(${MODULE} PUBLIC SQLite3)
