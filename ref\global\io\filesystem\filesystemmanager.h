﻿#ifndef FILESYSTEMMANAGER_H
#define FILESYSTEMMANAGER_H

#include <memory>
#include <vector>
#include <functional>

#ifdef USE_QT
#include <QStorageInfo>
#include <QThread>
#include <QObject>
#include <QVariantList>
#endif

#include "fileinfogatherer.h"

namespace filesystem {

class FileSystemManager
{
public:
    // Callback types
    using UpdatesCallback = std::function<void(const std::string& path, const std::vector<FileInfo>& updates)>;
    using DirectoryLoadedCallback = std::function<void(const std::string& path)>;

    FileSystemManager(const FileSystemManager&) = delete;
    FileSystemManager& operator=(const FileSystemManager&) = delete;

    static FileSystemManager& instance()
    {
        static FileSystemManager fsm;
        return fsm;
    }

    // Set callbacks
    void setUpdatesCallback(UpdatesCallback callback);
    void setDirectoryLoadedCallback(DirectoryLoadedCallback callback);

    // QVariantList getDriver();

    void getFileInfos(const std::string& path);

private:
    FileSystemManager();
    ~FileSystemManager() = default;

    std::unique_ptr<FileInfoGatherer> m_fileInfoGatherer;
    UpdatesCallback m_updatesCallback;
    DirectoryLoadedCallback m_directoryLoadedCallback;
};

}
#endif // FILESYSTEMMANAGER_H
