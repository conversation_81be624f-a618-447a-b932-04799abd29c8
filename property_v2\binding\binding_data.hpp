#pragma once

/**
 * @file binding_data.hpp
 * @brief 绑定数据管理
 * 
 * 管理属性的绑定数据，包括绑定设置、移除和观察者管理。
 * 
 * <AUTHOR> System V2
 * @date 2025
 */

#include <memory>
#include <atomic>
#include <vector>
#include "property_binding.hpp"
#include "../observer/observer_base.hpp"
#include "../core/memory.hpp"

namespace property_v2::binding {

// 前向声明
namespace observer { class property_observer; }

/**
 * @brief 绑定数据类
 * 
 * 管理单个属性的绑定和观察者信息。
 */
class binding_data {
public:
    /**
     * @brief 默认构造函数
     */
    binding_data() noexcept = default;
    
    /**
     * @brief 移动构造函数
     */
    binding_data(binding_data&& other) noexcept;
    
    /**
     * @brief 禁用拷贝
     */
    PROPERTY_DISABLE_COPY(binding_data)
    
    /**
     * @brief 移动赋值运算符
     */
    binding_data& operator=(binding_data&& other) noexcept;
    
    /**
     * @brief 析构函数
     */
    ~binding_data();
    
    /**
     * @brief 检查是否有绑定
     */
    bool has_binding() const noexcept;
    
    /**
     * @brief 设置绑定
     * @param new_binding 新的绑定
     * @param property_data 属性数据指针
     * @return 之前的绑定
     */
    untyped_property_binding set_binding(const untyped_property_binding& new_binding,
                                        untyped_property_data* property_data);
    
    /**
     * @brief 移除绑定
     * @return 被移除的绑定
     */
    untyped_property_binding remove_binding();
    
    /**
     * @brief 获取当前绑定
     */
    untyped_property_binding get_binding() const;
    
    /**
     * @brief 注册到当前正在求值的绑定
     */
    void register_with_current_binding() const;
    
    /**
     * @brief 通知观察者
     * @param property_data 属性数据指针
     */
    void notify_observers(untyped_property_data* property_data) const;
    
    /**
     * @brief 添加观察者
     * @param observer 观察者指针
     */
    void add_observer(observer::property_observer* observer);
    
    /**
     * @brief 移除观察者
     * @param observer 观察者指针
     */
    void remove_observer(observer::property_observer* observer);
    
    /**
     * @brief 获取观察者数量
     */
    std::size_t observer_count() const noexcept;

private:
    // 使用位标记来区分绑定和观察者列表
    static constexpr std::uintptr_t binding_bit = 0x1;
    static constexpr std::uintptr_t delayed_notification_bit = 0x2;
    
    mutable std::atomic<std::uintptr_t> data_ptr_{0};  ///< 数据指针（带标记位）
    
    /**
     * @brief 获取数据指针引用
     */
    std::uintptr_t& data_ref() const noexcept {
        return reinterpret_cast<std::uintptr_t&>(data_ptr_.load(std::memory_order_relaxed));
    }
    
    /**
     * @brief 获取绑定指针
     */
    binding_private* get_binding_ptr() const noexcept;
    
    /**
     * @brief 获取第一个观察者
     */
    observer::property_observer* get_first_observer() const noexcept;
    
    /**
     * @brief 设置第一个观察者
     */
    void set_first_observer(observer::property_observer* observer) noexcept;
    
    /**
     * @brief 检查是否延迟通知
     */
    bool is_notification_delayed() const noexcept {
        return (data_ptr_.load(std::memory_order_relaxed) & delayed_notification_bit) != 0;
    }
};

/**
 * @brief 绑定存储类
 * 
 * 为对象提供绑定数据的存储和管理。
 */
class binding_storage {
public:
    /**
     * @brief 构造函数
     */
    binding_storage() noexcept;
    
    /**
     * @brief 析构函数
     */
    ~binding_storage();
    
    /**
     * @brief 禁用拷贝和移动
     */
    PROPERTY_DISABLE_COPY_MOVE(binding_storage)
    
    /**
     * @brief 检查是否为空
     */
    bool is_empty() const noexcept { return !storage_data_; }
    
    /**
     * @brief 获取属性的绑定数据
     * @param property_data 属性数据指针
     * @param create 如果不存在是否创建
     * @return 绑定数据指针
     */
    binding_data* get_binding_data(const untyped_property_data* property_data, 
                                  bool create = false);
    
    /**
     * @brief 注册依赖
     * @param property_data 属性数据指针
     */
    void register_dependency(const untyped_property_data* property_data) const;
    
    /**
     * @brief 清除所有绑定数据
     */
    void clear();

private:
    struct storage_data;
    std::unique_ptr<storage_data> storage_data_;  ///< 存储数据
    
    /**
     * @brief 扩容存储
     */
    void expand_storage();
};

/**
 * @brief 属性更新组
 * 
 * 在更新组内，属性变更不会立即触发通知，而是延迟到组结束时批量处理。
 */
class property_update_group {
public:
    /**
     * @brief 开始属性更新组
     */
    static void begin() noexcept;
    
    /**
     * @brief 结束属性更新组
     */
    static void end() noexcept;
    
    /**
     * @brief 检查是否在更新组中
     */
    static bool is_in_update_group() noexcept;

private:
    static thread_local std::atomic<int> update_group_depth_;
};

/**
 * @brief 作用域属性更新组
 * 
 * RAII风格的属性更新组管理。
 */
class scoped_property_update_group {
public:
    /**
     * @brief 构造函数，开始更新组
     */
    scoped_property_update_group() noexcept {
        property_update_group::begin();
    }
    
    /**
     * @brief 析构函数，结束更新组
     */
    ~scoped_property_update_group() noexcept {
        property_update_group::end();
    }
    
    /**
     * @brief 禁用拷贝和移动
     */
    PROPERTY_DISABLE_COPY_MOVE(scoped_property_update_group)
};

} // namespace property_v2::binding
