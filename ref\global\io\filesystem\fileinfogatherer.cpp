﻿#include "fileinfogatherer.h"

#include <regex>

#ifdef _WIN32
#include <windows.h>
#endif

namespace fs = std::filesystem;
using namespace filesystem;

FileInfoGatherer::~FileInfoGatherer()
{
    abort();
    wait();
}

void FileInfoGatherer::setUpdatesCallback(UpdatesCallback callback)
{
    std::lock_guard<std::mutex> lock(m_mutex);
    m_updatesCallback = std::move(callback);
}

void FileInfoGatherer::setDirectoryLoadedCallback(DirectoryLoadedCallback callback)
{
    std::lock_guard<std::mutex> lock(m_mutex);
    m_directoryLoadedCallback = std::move(callback);
}

void FileInfoGatherer::setErrorCallback(ErrorCallback callback)
{
    std::lock_guard<std::mutex> lock(m_mutex);
    m_errorCallback = std::move(callback);
}

void FileInfoGatherer::setProgressCallback(ProgressCallback callback)
{
    std::lock_guard<std::mutex> lock(m_mutex);
    m_progressCallback = std::move(callback);
}

std::vector<FileInfo> FileInfoGatherer::getFileInfos(
    const std::string& path,
    GatherType type,
    const std::vector<std::string>& nameFilters,
    bool includeFiles,
    bool includeDirs)
{
    // Get future from async operation
    auto future = getFileInfosAsync(Path(path), type, nameFilters, includeFiles, includeDirs);

    // Wait for completion
    future.wait();

    // Return result
    return future.get();
}

std::future<std::vector<FileInfo>> FileInfoGatherer::getFileInfosAsync(
    const Path& path,
    GatherType type,
    const std::vector<std::string>& nameFilters,
    bool includeFiles,
    bool includeDirs)
{
    // Abort any running operation
    abort();
    wait();

    // Reset state
    m_abort.store(false);
    m_running.store(true);
    m_filesProcessed = 0;
    m_totalFiles = 0;
    m_hasCalledUpdate = false;
    m_lastUpdateTime = std::chrono::steady_clock::now();

    // Create promise and future
    std::promise<std::vector<FileInfo>> promise;
    std::future<std::vector<FileInfo>> future = promise.get_future();

    // Start worker thread
    m_workerThread = std::thread(&FileInfoGatherer::gatherWorker, this,
                                 path, type, nameFilters, includeFiles, includeDirs,
                                 std::move(promise));
    m_workerThread.detach();

    return future;
}

void FileInfoGatherer::abort()
{
    m_abort.store(true);
}

bool FileInfoGatherer::isRunning() const
{
    return m_running.load();
}

bool FileInfoGatherer::wait(int timeout)
{
    if (!isRunning()) {
        return true;
    }

    std::unique_lock<std::mutex> lock(m_mutex);
    if (timeout <= 0) {
        m_condition.wait(lock, [this] { return !m_running.load(); });
        return true;
    } else {
        return m_condition.wait_for(lock, std::chrono::milliseconds(timeout),
                                    [this] { return !m_running.load(); });
    }
}

void FileInfoGatherer::gatherWorker(
    const Path& path,
    GatherType type,
    const std::vector<std::string>& nameFilters,
    bool includeFiles,
    bool includeDirs,
    std::promise<std::vector<FileInfo>> promise)
{
    try {
        std::vector<FileInfo> result;

        // Process the directory
        processDirectory(path, type, nameFilters, includeFiles, includeDirs, result);

        // Set the promise value
        promise.set_value(std::move(result));

        // Notify directory loaded
        if (m_directoryLoadedCallback) {
            m_directoryLoadedCallback(path.toString());
        }
    }
    catch (const std::exception& e) {
        // Set the promise exception
        promise.set_exception(std::current_exception());

        // Notify error
        if (m_errorCallback) {
            m_errorCallback(path.toString(), e.what());
        }
    }

    // Mark as not running
    m_running.store(false);

    // Notify waiting threads
    m_condition.notify_all();
}

void FileInfoGatherer::processDirectory(
    const Path& pathIn,
    GatherType type,
    const std::vector<std::string>& nameFilters,
    bool includeFiles,
    bool includeDirs,
    std::vector<FileInfo>& result)
{
    try {
        fs::path path(pathIn.toWString());
        // Check if the directory exists
        if (!fs::exists(path) || !fs::is_directory(path)) {
            if (m_errorCallback) {
                m_errorCallback(path.string(), "Path does not exist or is not a directory");
            }
            return;
        }

        // Count total files for progress reporting
        if (m_totalFiles == 0) {
            m_totalFiles = std::distance(fs::directory_iterator(path), fs::directory_iterator());
        }

        // Process directory entries
        for (const auto& entry : fs::directory_iterator(path, fs::directory_options::skip_permission_denied)) {
            // Check if aborted
            if (m_abort.load()) {
                return;
            }

            // Get file name
            fs::path fspath = entry.path();
            std::string name = fspath.filename().string();

            // Skip "." and ".."
            if (name == "." || name == "..") continue;

            // Skip if it doesn't match the filter (do this first as it's cheapest)
            if (!nameFilters.empty() && !matchesFilter(name, nameFilters)) {
                continue;
            }

            // Create a temporary entry to check basic properties without creating a FileInfo yet
            bool isFileType = entry.is_regular_file();
            bool isDirType = entry.is_directory();

            // Skip if it's a file and we don't want files
            if (isFileType && !includeFiles) {
                continue;
            }

            // Skip if it's a directory and we don't want directories
            if (isDirType && !includeDirs) {
                continue;
            }

            // Add to result
            result.emplace_back(FileInfo(std::move(entry)));

            // Update progress
            m_filesProcessed++;
            if (m_progressCallback) {
                m_progressCallback(m_filesProcessed, m_totalFiles);
            }

            if (result.size() >= 100) {
                auto currentTime = std::chrono::steady_clock::now();
                auto elapsedTime = std::chrono::duration_cast<std::chrono::seconds>(
                                       currentTime - m_lastUpdateTime).count();

                if ((!m_hasCalledUpdate && elapsedTime > .1) || elapsedTime >= 1) {
                    if (m_updatesCallback) {
                        m_updatesCallback(path.string(), result);
                        m_hasCalledUpdate = true;
                        m_lastUpdateTime = currentTime;
                    }
                    result.clear();
                }
            }

            // Recursive processing
            if (type == GatherType::Recursive && isDirType) {
                processDirectory(Path(fspath.wstring()), type, nameFilters, includeFiles, includeDirs, result);
            }
        }

        // Send remaining updates
        if (!result.empty() && m_updatesCallback) {
            m_updatesCallback(pathIn.toString(), result);
        }
    }
    catch (const std::exception& e) {
        if (m_errorCallback) {
            m_errorCallback(pathIn.toString(), e.what());
            m_hasCalledUpdate = true;
            m_lastUpdateTime = std::chrono::steady_clock::now();
        }
    }
}

bool FileInfoGatherer::matchesFilter(const std::string& name, const std::vector<std::string>& filters)
{
    for (const auto& filter : filters) {
        // Convert glob pattern to regex
        std::string regexPattern = filter;
        // Replace . with \. (escape dot)
        size_t pos = 0;
        while ((pos = regexPattern.find(".", pos)) != std::string::npos) {
            regexPattern.replace(pos, 1, "\\.");
            pos += 2;
        }
        // Replace * with .*
        pos = 0;
        while ((pos = regexPattern.find("*", pos)) != std::string::npos) {
            regexPattern.replace(pos, 1, ".*");
            pos += 2;
        }
        // Replace ? with .
        pos = 0;
        while ((pos = regexPattern.find("?", pos)) != std::string::npos) {
            regexPattern.replace(pos, 1, ".");
            pos += 1;
        }

        std::regex regex(regexPattern, std::regex_constants::icase);
        if (std::regex_match(name, regex)) {
            return true;
        }
    }

    return false;
}

/*void FileInfoGatherer::getFileInfos(const std::string& path)
{
    auto startTime = std::chrono::steady_clock::now();
    auto base = std::chrono::steady_clock::now();
    bool firstTime = true;
    int level = 0;
    m_updatedFiles.clear();

    getFiles(path, level, base, firstTime);

    if (!m_updatedFiles.empty() && m_updatesCallback) {
        m_updatesCallback(path, m_updatedFiles);
    }

    auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
                       std::chrono::steady_clock::now() - startTime).count();
    std::cout << "File info gathered. " << path << " " << elapsed << "ms" << std::endl;

    if (m_directoryLoadedCallback) {
        m_directoryLoadedCallback(path);
    }
}

void FileInfoGatherer::getFiles(const std::string& path, int& level,
                                std::chrono::steady_clock::time_point& base, bool& firstTime)
{
    try {
        fs::path dirPath(path);
        auto dirIt = fs::directory_iterator(dirPath, fs::directory_options::skip_permission_denied);

        for (const auto& entry : dirIt) {
            if (m_abort.load()) break;

            // Skip "." and ".."
            const auto& filename = entry.path().filename();
            if (filename == "." || filename == "..") continue;

            // Create FileInfo and process it
            FileInfo fileInfo(entry);
            fetch(path, fileInfo, base, firstTime);

            // Recursive directory traversal (commented out for now)
            // if (level > 0 && fileInfo.isDir()) {
            //     level -= 1;
            //     getFiles(fileInfo.filePath(), level, base, firstTime);
            // }
        }
    } catch (const fs::filesystem_error& e) {
        std::cerr << "Filesystem error: " << e.what() << std::endl;
    }
}

void FileInfoGatherer::fetch(const std::string& path, const FileInfo& fileInfo,
                             std::chrono::steady_clock::time_point& base, bool& firstTime)
{
    m_updatedFiles.push_back(fileInfo);
    auto current = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(current - base).count();

    if ((firstTime && m_updatedFiles.size() > 100) || elapsed > 1000) {
        if (m_updatesCallback) {
            m_updatesCallback(path, m_updatedFiles);
        }
        m_updatedFiles.clear();
        base = current;
        firstTime = false;
    }
}*/
