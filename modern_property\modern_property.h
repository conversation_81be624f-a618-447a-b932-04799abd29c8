#pragma once

/**
 * @file modern_property.h
 * @brief Modern Property System - Main Header
 * 
 * This is the main header file for the modern C++20 property binding system.
 * Include this file to get access to all property system functionality.
 * 
 * <AUTHOR> Property System
 * @version 1.0
 * @date 2024
 * 
 * ## Features
 * 
 * - **Type Safety**: Full compile-time type checking using C++20 concepts
 * - **Modern C++**: Leverages C++20 features like concepts, source_location, ranges
 * - **Memory Safety**: RAII, smart pointers, and automatic resource management
 * - **Thread Safety**: Thread-safe binding storage and evaluation
 * - **Performance**: Efficient parameter passing and memory layout
 * - **Debugging**: Source location tracking for binding errors
 * - **API Compatibility**: Maintains compatibility with original property system
 * 
 * ## Basic Usage
 * 
 * ```cpp
 * #include "modern_property.h"
 * using namespace modern_property;
 * 
 * // Create properties
 * property<int> width{100};
 * property<int> height{50};
 * property<int> area;
 * 
 * // Set up binding
 * area.setBinding([&] { return width.value() * height.value(); });
 * 
 * // Use properties
 * std::cout << "Area: " << area.value() << std::endl; // Output: 5000
 * 
 * width = 200; // This will automatically update area
 * std::cout << "New area: " << area.value() << std::endl; // Output: 10000
 * 
 * // Add observers
 * area.onValueChanged([] { std::cout << "Area changed!" << std::endl; });
 * ```
 * 
 * ## Advanced Features
 * 
 * - Circular dependency detection
 * - Priority-based observer ordering  
 * - Batched notifications
 * - Custom error handling
 * - Type-safe binding creation
 * - Memory-efficient storage
 */

// Core system headers
#include "core/types.h"
#include "core/property_data.h"
#include "core/property_binding.h"
#include "core/property_observer.h"
#include "core/property.h"
#include "core/binding_storage.h"

/**
 * @namespace modern_property
 * @brief Main namespace for the modern property system
 * 
 * This namespace contains all classes, functions, and utilities
 * for the modern C++20 property binding system.
 */
namespace modern_property {

/**
 * @brief Convenience aliases for common property types
 */
namespace types {
    /// @brief Integer property
    using int_property = property<int>;
    
    /// @brief Floating-point property
    using double_property = property<double>;
    
    /// @brief Boolean property
    using bool_property = property<bool>;
    
    /// @brief String property
    using string_property = property<string_type>;
    
    /// @brief Size property
    using size_property = property<size_type>;
}

/**
 * @brief Utility functions for property system
 */
namespace utils {
    /**
     * @brief Begin a notification batch across all properties
     * 
     * This is a global convenience function that begins notification batching
     * for the thread-local notification manager.
     */
    inline void begin_property_update_group() {
        // Access thread-local notification manager
        thread_local notification_manager manager;
        manager.begin_batch();
    }
    
    /**
     * @brief End a notification batch across all properties
     * 
     * This complements begin_property_update_group() and sends all
     * queued notifications.
     */
    inline void end_property_update_group() {
        thread_local notification_manager manager;
        manager.end_batch();
    }
    
    /**
     * @brief RAII guard for property update groups
     * 
     * This provides automatic batch management using RAII principles.
     * 
     * @code
     * {
     *     auto guard = utils::scoped_property_update_group();
     *     // Multiple property changes here will be batched
     *     prop1 = new_value1;
     *     prop2 = new_value2;
     *     prop3 = new_value3;
     * } // All notifications sent here when guard destructs
     * @endcode
     */
    [[nodiscard]] inline auto scoped_property_update_group() {
        thread_local notification_manager manager;
        return notification_batch_guard{manager};
    }
    
    /**
     * @brief Create a property with automatic type deduction
     * @tparam T Property type (deduced from initial value)
     * @param initial_value Initial value for the property
     * @return New property with deduced type
     */
    template<typename T>
    auto make_property(T&& initial_value) {
        return property<std::decay_t<T>>{std::forward<T>(initial_value)};
    }
    
    /**
     * @brief Create a bound property with automatic type deduction
     * @tparam F Function type (deduced)
     * @param binding_func Function that computes the property value
     * @param location Source location for debugging
     * @return New property with binding
     */
    template<typename F>
    auto make_bound_property(F&& binding_func,
                           const source_location_type& location = source_location_type::current()) {
        return property_factory::create_with_binding(std::forward<F>(binding_func), location);
    }
}

/**
 * @brief Version information
 */
namespace version {
    /// @brief Major version number
    constexpr int major = 1;
    
    /// @brief Minor version number
    constexpr int minor = 0;
    
    /// @brief Patch version number
    constexpr int patch = 0;
    
    /// @brief Version string
    constexpr const char* string = "1.0.0";
    
    /// @brief Build information
    constexpr const char* build_info = "Modern C++20 Property System";
}

/**
 * @brief Feature test macros
 */
#define MODERN_PROPERTY_HAS_CONCEPTS 1
#define MODERN_PROPERTY_HAS_SOURCE_LOCATION 1
#define MODERN_PROPERTY_HAS_RANGES 1
#define MODERN_PROPERTY_VERSION_MAJOR 1
#define MODERN_PROPERTY_VERSION_MINOR 0
#define MODERN_PROPERTY_VERSION_PATCH 0

} // namespace modern_property

/**
 * @brief Convenience macros for common operations
 */

/**
 * @brief Create a property with given name and initial value
 * @param name Property variable name
 * @param type Property value type
 * @param value Initial value
 */
#define MODERN_PROPERTY_DECLARE(name, type, value) \
    modern_property::property<type> name{value}

/**
 * @brief Create a bound property with given name and binding function
 * @param name Property variable name
 * @param func Binding function
 */
#define MODERN_PROPERTY_BIND(name, func) \
    auto name = modern_property::utils::make_bound_property(func)

/**
 * @brief Create a scoped property update group
 */
#define MODERN_PROPERTY_UPDATE_GROUP() \
    auto MODERN_PROPERTY_UNIQUE_NAME(guard) = modern_property::utils::scoped_property_update_group()

/**
 * @brief Helper for generating unique variable names in macros
 */
#define MODERN_PROPERTY_UNIQUE_NAME_IMPL(prefix, line) prefix##line
#define MODERN_PROPERTY_UNIQUE_NAME(prefix) MODERN_PROPERTY_UNIQUE_NAME_IMPL(prefix, __LINE__) 