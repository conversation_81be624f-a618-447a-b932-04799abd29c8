﻿set(MODULE io)
set(MODULE_SRC
    standardpaths.h
    standardpaths_linux.cpp
    standardpaths_mac.mm
    standardpaths_win.cpp

    # ifilesystem.h

    # filesystem
    filesystem/path.h
    filesystem/path.cpp
    filesystem/iodevice.h
    filesystem/iodevice.cpp
    filesystem/buffer.h
    filesystem/buffer.cpp
    filesystem/fileinfo.h
    filesystem/fileinfo.cpp
    filesystem/file.h
    filesystem/file.cpp
    filesystem/folder.h
    filesystem/folder.cpp
    filesystem/filesystem.h
    filesystem/filesystem.cpp

    # filesystem/casecorrectpath.h
    # filesystem/ifilesystemservice.h
    filesystem/fileutils.h
    filesystem/fileutils.cpp
    filesystem/fileinfogatherer.h
    filesystem/fileinfogatherer.cpp
    filesystem/filesystemmanager.h
    filesystem/filesystemmanager.cpp
)

add_library(${MODULE})

target_sources(${MODULE} PRIVATE
    ${MODULE_SRC}
    )

target_include_directories(${MODULE} PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/filesystem
)

target_link_libraries(${MODULE} PUBLIC ${MODULE_LINK})
