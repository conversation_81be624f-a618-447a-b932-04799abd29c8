﻿#ifndef METATYPE_H
#define METATYPE_H

#include <type_traits>

#include "typetraits.h"

// #if __cplusplus < 202002L
// using char8_t = unsigned char;
// namespace std {
// using u8string = string;
// }
// #endif

// using Nullptr = std::nullptr_t;
// // std::is_integral_v
// using Char = char;
// using SChar = signed char;
// using UChar = unsigned char;
// using WChar = wchar_t;
// using Char8 = char8_t;
// using Char16 = char16_t;
// using Char32 = char32_t;
// using Bool = bool;
// using Short = short;
// using UShort = unsigned short;
// using Int = int;
// using UInt = unsigned int;
// using Long = long;
// using ULong = unsigned long;
// using LongLong = long long;
// using ULongLong = unsigned long long;
// // std::is_floating_point_v
// using Float = float;
// using Double = double;
// using LongDouble = long double;

using String = std::string;
// using WString = std::wstring;
// using U8String = std::u8string;
// using U16String = std::u16string;
// using U32String = std::u32string;

class MetaType {
public:
    enum Type {
        UnknownType, //
        Void,        //	void
        Bool,        //	bool
        Char,        // char
        SChar,       // signed char
        UChar,       // unsigned char
        WChar,       // wchar_t
        Char8,       // char8_t
        Char16,      // char16_t
        Char32,      // char32_t
        Short,       // short;
        UShort,      // unsigned short;
        Int,         //	int
        UInt,        //	unsigned int
        Long,        //	long
        ULong,       //	unsigned long
        LongLong,    //	long long
        ULongLong,   //	unsigned long long
        Float,       //	float
        Double,      //	double
        LongDouble,  //	long double

        Nullptr,     //	std::nullptr_t
        VoidStar,    //	void *

        UserType     //
    };

    constexpr static MetaType fromType();

    template<typename T>
    constexpr static MetaType fromType()
    {
        return MetaType();
    }
    size_t id() const { return 0;}
};

template <typename T>
inline constexpr int MetaTypeId()
{
    return MetaType::fromType<T>().id();
}

struct MetaTypeInfo {
    const char* name;
    int typeId;
};

class Variant;
class View {
public:
    View() = default;
    View(std::string str) : m_str(str) {}
    View(const char* str) : m_str(str) {}
    operator std::string() const { return m_str; }
    View operator+(const View& other) const { return View(m_str + other.m_str); }
    View operator+(const char* other) const { return View(m_str + std::string(other)); }
    View operator+(const std::string& other) const { return View(m_str + other); }
    void operator+=(const View& other) { m_str += other.m_str; }
    void operator+=(const char* other) { m_str += std::string(other); }
    void operator+=(const std::string& other) { m_str += other; }

private:
    std::string m_str;
};

template <typename T>
std::string _typename() {
    if constexpr (std::is_same_v<T, const char*> || std::is_same_v<T, char*>)
        return "chars";
    if constexpr (std::is_same_v<T, const signed char*> || std::is_same_v<T, signed char*>)
        return "signed chars";
    if constexpr (std::is_same_v<T, const unsigned char*> || std::is_same_v<T, unsigned char*>)
        return "unsigned chars";
    if constexpr (std::is_same_v<T, const wchar_t*> || std::is_same_v<T, wchar_t*>)
        return "wchars";
    // if constexpr (std::is_same_v<T, const char8_t*> || std::is_same_v<T, char8_t*>)
    //     return "char8s";
    if constexpr (std::is_same_v<T, const char16_t*> || std::is_same_v<T, char16_t*>)
        return "char16s";
    if constexpr (std::is_same_v<T, const char32_t*> || std::is_same_v<T, char32_t*>)
        return "char32s";
    if constexpr (std::is_same_v<T, char const*>)
        return "char const*";
    if constexpr (is_string_v<T>)
        return "string";
    if constexpr (is_vector_v<T>)
        return "vector(" + _typename<typename T::value_type>() + ")";
    if constexpr (is_map_v<T>)
        return "map";
    if constexpr (is_unordered_map_v<T>)
        return "hash";
    if constexpr (is_pair_v<T>)
        return "pair";
    if constexpr (is_set_v<T>)
        return "set";
    if constexpr (std::is_same_v<T, Variant>)
        return "Variant";
    return typeid(T).name();
}

#endif // METATYPE_H
