#pragma once

/**
 * @file property.hpp
 * @brief 主属性类实现
 * 
 * 实现完整的属性功能，包括值读写、绑定管理和变更通知。
 * 
 * <AUTHOR> System V2
 * @date 2025
 */

#include <utility>
#include <functional>
#include "property_data.hpp"
#include "../binding/binding_data.hpp"
#include "../binding/property_binding.hpp"
#include "../observer/change_handler.hpp"
#include "../core/concepts.hpp"
#include "../core/utils.hpp"

namespace property_v2::property {

/**
 * @brief 主属性类
 * 
 * 提供完整的属性功能，包括：
 * - 值的读取和设置
 * - 绑定的创建和管理
 * - 观察者的注册和通知
 * - 类型安全的操作接口
 * 
 * @tparam T 属性值类型
 */
template<typename T>
class property : public property_data<T> {
public:
    using value_type = T;
    using parameter_type = core::parameter_type_t<T>;
    using rvalue_ref = core::rvalue_reference_type_t<T>;
    using arrow_operator_result = core::arrow_operator_result_t<T>;
    using binding_type = binding::property_binding<T>;
    
    /**
     * @brief 默认构造函数
     */
    property() = default;
    
    /**
     * @brief 值构造函数
     * @param initial_value 初始值
     */
    explicit property(parameter_type initial_value) 
        : property_data<T>(initial_value) {}
    
    /**
     * @brief 右值构造函数
     * @param initial_value 初始值（右值）
     */
    explicit property(rvalue_ref initial_value) 
        : property_data<T>(std::move(initial_value)) {}
    
    /**
     * @brief 绑定构造函数
     * @param binding 属性绑定
     */
    explicit property(const binding_type& binding) : property() {
        set_binding(binding);
    }
    
    /**
     * @brief 函数对象构造函数
     * @tparam F 函数对象类型
     * @param functor 函数对象
     * @param location 源位置
     */
    template<typename F>
        requires core::Nullary_invocable_r<F, T>
    explicit property(F&& functor, 
                     const core::source_location& location = PROPERTY_DEFAULT_BINDING_LOCATION)
        : property(binding_type(std::forward<F>(functor), location)) {}
    
    /**
     * @brief 禁用拷贝和移动
     * 
     * 属性对象通常绑定到特定的内存位置，不应该被拷贝或移动。
     */
    PROPERTY_DISABLE_COPY_MOVE(property)
    
    /**
     * @brief 析构函数
     */
    ~property() = default;
    
    /**
     * @brief 获取属性值
     * 
     * 如果属性有绑定，会触发绑定求值。
     * 同时会注册到当前正在求值的绑定作为依赖。
     * 
     * @return 属性的当前值
     */
    parameter_type value() const {
        binding_data_.register_with_current_binding();
        return this->value_bypassing_bindings();
    }
    
    /**
     * @brief 设置属性值
     * 
     * 设置新值会移除现有绑定，并在值确实发生变化时通知观察者。
     * 
     * @param new_value 新值
     */
    void set_value(parameter_type new_value) {
        binding_data_.remove_binding();
        if (!this->is_equal(new_value)) {
            this->set_value_bypassing_bindings(new_value);
            notify_observers();
        }
    }
    
    /**
     * @brief 设置属性值（右值版本）
     * 
     * @param new_value 新值（右值）
     */
    void set_value(rvalue_ref new_value) {
        binding_data_.remove_binding();
        if (!this->is_equal(new_value)) {
            this->set_value_bypassing_bindings(std::move(new_value));
            notify_observers();
        }
    }
    
    /**
     * @brief 赋值运算符
     * 
     * @param new_value 新值
     * @return 属性引用
     */
    property& operator=(parameter_type new_value) {
        set_value(new_value);
        return *this;
    }
    
    /**
     * @brief 赋值运算符（右值版本）
     * 
     * @param new_value 新值（右值）
     * @return 属性引用
     */
    property& operator=(rvalue_ref new_value) {
        set_value(std::move(new_value));
        return *this;
    }
    
    /**
     * @brief 类型转换运算符
     * 
     * 允许属性在需要值的地方自动转换。
     * 
     * @return 属性值
     */
    operator parameter_type() const {
        return value();
    }
    
    /**
     * @brief 箭头运算符
     * 
     * 为智能指针类型提供箭头运算符支持。
     * 
     * @return 箭头运算符结果
     */
    arrow_operator_result operator->() const {
        if constexpr (core::Dereferenceable<T>) {
            return value();
        } else if constexpr (core::Pointer<T>) {
            value();  // 触发依赖注册
            return this->value_;
        } else {
            static_assert(core::Dereferenceable<T> || core::Pointer<T>, 
                         "Arrow operator is only available for dereferenceable or pointer types");
        }
    }
    
    /**
     * @brief 解引用运算符
     * 
     * @return 属性值
     */
    parameter_type operator*() const {
        return value();
    }
    
    /**
     * @brief 设置绑定
     * 
     * @param new_binding 新的绑定
     * @return 之前的绑定
     */
    binding_type set_binding(const binding_type& new_binding) {
        auto old_binding = binding_data_.set_binding(new_binding, this);
        return binding_type(old_binding);
    }
    
    /**
     * @brief 设置绑定（未类型化版本）
     * 
     * @param new_binding 新的绑定
     * @return 是否设置成功
     */
    bool set_binding(const binding::untyped_property_binding& new_binding) {
        // 这里应该检查类型匹配，简化实现暂时跳过
        set_binding(binding_type(new_binding));
        return true;
    }
    
    /**
     * @brief 设置绑定（函数对象版本）
     * 
     * @tparam F 函数对象类型
     * @param functor 函数对象
     * @param location 源位置
     * @return 之前的绑定
     */
    template<typename F>
        requires core::Nullary_invocable_r<F, T>
    binding_type set_binding(F&& functor, 
                            const core::source_location& location = PROPERTY_DEFAULT_BINDING_LOCATION) {
        return set_binding(binding::make_property_binding(std::forward<F>(functor), location));
    }
    
    /**
     * @brief 检查是否有绑定
     * 
     * @return 是否有绑定
     */
    bool has_binding() const noexcept {
        return binding_data_.has_binding();
    }
    
    /**
     * @brief 获取当前绑定
     * 
     * @return 当前绑定
     */
    binding_type binding() const {
        return binding_type(binding_data_.get_binding());
    }
    
    /**
     * @brief 取走绑定
     * 
     * 移除并返回当前绑定。
     * 
     * @return 被移除的绑定
     */
    binding_type take_binding() {
        return binding_type(binding_data_.remove_binding());
    }
    
    /**
     * @brief 注册值变更处理器
     * 
     * @tparam F 处理函数类型
     * @param handler 处理函数
     * @return 变更处理器对象
     */
    template<typename F>
        requires core::Nullary_invocable<F>
    observer::property_change_handler<F> on_value_changed(F&& handler) {
        return observer::property_change_handler<F>(*this, std::forward<F>(handler));
    }
    
    /**
     * @brief 订阅值变更（立即调用一次）
     * 
     * @tparam F 处理函数类型
     * @param handler 处理函数
     * @return 变更处理器对象
     */
    template<typename F>
        requires core::Nullary_invocable<F>
    observer::property_change_handler<F> subscribe(F&& handler) {
        handler();  // 立即调用一次
        return on_value_changed(std::forward<F>(handler));
    }
    
    /**
     * @brief 添加通知器
     * 
     * @tparam F 处理函数类型
     * @param handler 处理函数
     * @return 通知器对象
     */
    template<typename F>
        requires core::Nullary_invocable<F>
    observer::property_notifier add_notifier(F&& handler) {
        return observer::property_notifier(*this, std::forward<F>(handler));
    }
    
    /**
     * @brief 获取绑定数据
     * 
     * 主要用于内部实现和观察者注册。
     * 
     * @return 绑定数据引用
     */
    const binding::binding_data& binding_data() const noexcept {
        return binding_data_;
    }
    
    /**
     * @brief 获取绑定数据（非常量版本）
     * 
     * @return 绑定数据引用
     */
    binding::binding_data& binding_data() noexcept {
        return binding_data_;
    }

private:
    binding::binding_data binding_data_;  ///< 绑定数据
    
    /**
     * @brief 通知观察者
     */
    void notify_observers() {
        binding_data_.notify_observers(this);
    }
};

/**
 * @brief 创建从另一个属性到当前属性的绑定
 *
 * @tparam T 属性值类型
 * @param other_property 源属性
 * @param location 源位置
 * @return 属性绑定对象
 */
template<typename T>
binding::property_binding<T> make_property_binding(
    const property<T>& other_property,
    const core::source_location& location = PROPERTY_DEFAULT_BINDING_LOCATION) {
    return binding::make_property_binding([&other_property]() -> T {
        return other_property.value();
    }, location);
}

/**
 * @brief 属性比较运算符
 *
 * @tparam T 属性值类型
 * @param lhs 第一个属性
 * @param rhs 第二个属性
 * @return 是否相等
 */
template<typename T>
    requires core::Equality_comparable<T>
bool operator==(const property<T>& lhs, const property<T>& rhs) {
    return lhs.value() == rhs.value();
}

/**
 * @brief 属性与值的比较运算符
 *
 * @tparam T 属性值类型
 * @param lhs 属性
 * @param rhs 值
 * @return 是否相等
 */
template<typename T>
    requires core::Equality_comparable<T>
bool operator==(const property<T>& lhs, core::parameter_type_t<T> rhs) {
    return lhs.value() == rhs;
}

/**
 * @brief 值与属性的比较运算符
 *
 * @tparam T 属性值类型
 * @param lhs 值
 * @param rhs 属性
 * @return 是否相等
 */
template<typename T>
    requires core::Equality_comparable<T>
bool operator==(core::parameter_type_t<T> lhs, const property<T>& rhs) {
    return lhs == rhs.value();
}

} // namespace property_v2::property
