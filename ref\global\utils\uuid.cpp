﻿#include "uuid.h"

#ifdef QT_CORE_LIB
#include <QUuid>
#elif defined(_WIN32)
#include <windows.h>
#include <objbase.h>
#elif defined(__linux__)
#include <uuid/uuid.h>
#else
#include <random>
#endif

Uuid::UuidArray Uuid::createUuid() {
#ifdef QT_CORE_LIB
    // Qt
    QUuid uuid = QUuid::createUuid();
    UUIDArray arr;
    memcpy(arr.data(), &uuid, UUID_SIZE);
    return arr;

#elif defined(_WIN32)
    // Windows
    UuidArray arr;
    GUID guid;
    CoCreateGuid(&guid);

    // Convert the GUID structure to consecutive bytes
    arr[0] = static_cast<uint8_t>((guid.Data1 >> 24) & 0xFF);
    arr[1] = static_cast<uint8_t>((guid.Data1 >> 16) & 0xFF);
    arr[2] = static_cast<uint8_t>((guid.Data1 >> 8) & 0xFF);
    arr[3] = static_cast<uint8_t>(guid.Data1 & 0xFF);

    arr[4] = static_cast<uint8_t>((guid.Data2 >> 8) & 0xFF);
    arr[5] = static_cast<uint8_t>(guid.Data2 & 0xFF);

    arr[6] = static_cast<uint8_t>((guid.Data3 >> 8) & 0xFF);
    arr[7] = static_cast<uint8_t>(guid.Data3 & 0xFF);

    for (int i = 0; i < 8; ++i) {
        arr[i + 8] = guid.Data4[i];
    }
    return arr;

#elif defined(__linux__)
    // Linux
    uuid_t uuid;
    uuid_generate(uuid);
    UUIDArray arr;
    memcpy(arr.data(), uuid, UUID_SIZE);
    return arr;

#else
    // Default, Use random number generator (RFC 4122 version 4)
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<uint16_t> dis(0, 255);

    // Generate 16 random bytes
    UuidArray arr;
    for (auto& byte : arr) {
        byte = static_cast<uint8_t>(dis(gen));
    }

    // Set the UUID version (Version 4 - random)
    arr[6] = (arr[6] & 0x0F) | 0x40;
    // Set the variant bit (RFC4122 variant)
    arr[8] = (arr[8] & 0x3F) | 0x80;

    return arr;

#endif
}

bool Uuid::isUuid(const std::string& s) {
    if (s.size() != 36)
        return false;

    for (size_t i = 0; i < s.size(); ++i) {
        if (i == 8 || i == 13 || i == 18 || i == 23) {
            if (s[i] != '-')
                return false;
        } else {
            if (!std::isxdigit(static_cast<unsigned char>(s[i])))
                return false;
        }
    }
    return true;
}
