#pragma once

#include "types.h"
#include "property_data.h"
#include <functional>
#include <memory>
#include <vector>
#include <ranges>
#include <algorithm>

/**
 * @brief Modern Property System - Property Observer
 * 
 * This file contains the property observer system that manages
 * change notifications and observer patterns for properties.
 */

namespace modern_property {

// Forward declarations
class property_observer_list;
class notification_manager;

/**
 * @brief Observer priority levels for notification ordering
 */
enum class observer_priority : std::uint8_t {
    lowest = 0,
    low = 64,
    normal = 128,
    high = 192,
    highest = 255
};

/**
 * @brief Base class for property observers
 * 
 * Property observers are notified when properties change their values.
 * They support priority-based ordering and automatic cleanup.
 */
class property_observer {
public:
    /// @brief Function type for change notifications
    using change_handler = std::function<void()>;
    
    /// @brief Function type for value change notifications with old/new values
    template<typename T>
    using value_change_handler = std::function<void(const T& old_value, const T& new_value)>;

    /**
     * @brief Constructor with change handler
     * @param handler Function to call when property changes
     * @param priority Priority level for this observer
     */
    explicit property_observer(change_handler handler, 
                             observer_priority priority = observer_priority::normal)
        : handler_{std::move(handler)}, priority_{priority} {}
    
    /// @brief Move constructor
    property_observer(property_observer&&) = default;
    
    /// @brief Move assignment
    property_observer& operator=(property_observer&&) = default;
    
    /// @brief Destructor automatically unregisters from observed properties
    ~property_observer() = default;
    
    MODERN_PROPERTY_DISABLE_COPY(property_observer)

    /**
     * @brief Notify this observer of a property change
     */
    void notify() const {
        if (handler_ && is_enabled_) {
            try {
                handler_();
            } catch (const std::exception& e) {
                // Log error but don't propagate exceptions from observers
                handle_observer_error(e);
            } catch (...) {
                // Log unknown error
                handle_observer_error();
            }
        }
    }
    
    /**
     * @brief Get the priority of this observer
     * @return Priority level
     */
    [[nodiscard]] observer_priority priority() const noexcept {
        return priority_;
    }
    
    /**
     * @brief Enable or disable this observer
     * @param enabled New enabled state
     */
    void set_enabled(bool enabled) noexcept {
        is_enabled_ = enabled;
    }
    
    /**
     * @brief Check if this observer is enabled
     * @return true if enabled
     */
    [[nodiscard]] bool is_enabled() const noexcept {
        return is_enabled_;
    }
    
    /**
     * @brief Get unique identifier for this observer
     * @return Pointer-based unique identifier
     */
    [[nodiscard]] const void* unique_id() const noexcept {
        return static_cast<const void*>(this);
    }

private:
    /**
     * @brief Handle exceptions from observer notifications
     * @param e Exception that occurred
     */
    void handle_observer_error(const std::exception& e) const noexcept {
        // In a real implementation, this would log to a proper logging system
        // For now, we'll just ignore errors to prevent cascading failures
        (void)e; // Suppress unused parameter warning
    }
    
    /**
     * @brief Handle unknown exceptions from observer notifications
     */
    void handle_observer_error() const noexcept {
        // In a real implementation, this would log to a proper logging system
    }

    change_handler handler_;
    observer_priority priority_;
    bool is_enabled_{true};
};

/**
 * @brief Typed observer for specific property types with value access
 * @tparam T Type of property being observed
 */
template<Property_storable T>
class typed_property_observer : public property_observer {
public:
    /// @brief Value change handler type for this property type
    using typed_change_handler = std::function<void(const T& old_value, const T& new_value)>;

    /**
     * @brief Constructor with typed change handler
     * @param handler Function to call with old and new values
     * @param priority Priority level for this observer
     */
    explicit typed_property_observer(typed_change_handler handler,
                                   observer_priority priority = observer_priority::normal)
        : property_observer{[]{}, priority}, typed_handler_{std::move(handler)} {}
    
    /**
     * @brief Notify with old and new values
     * @param old_value Previous value
     * @param new_value Current value
     */
    void notify_with_values(const T& old_value, const T& new_value) const {
        if (typed_handler_ && is_enabled()) {
            try {
                typed_handler_(old_value, new_value);
            } catch (const std::exception& e) {
                handle_observer_error(e);
            } catch (...) {
                handle_observer_error();
            }
        }
    }

private:
    typed_change_handler typed_handler_;
};

/**
 * @brief Observer list manages a collection of observers with priority ordering
 */
class property_observer_list {
public:
    /// @brief Container type for observers
    using observer_container = std::vector<std::unique_ptr<property_observer>>;

    /**
     * @brief Constructor
     */
    property_observer_list() = default;
    
    /// @brief Destructor
    ~property_observer_list() = default;
    
    /// @brief Move constructor
    property_observer_list(property_observer_list&&) = default;
    
    /// @brief Move assignment
    property_observer_list& operator=(property_observer_list&&) = default;
    
    MODERN_PROPERTY_DISABLE_COPY(property_observer_list)

    /**
     * @brief Add an observer to the list
     * @param observer Observer to add (moved)
     * @return Reference to the added observer
     */
    property_observer& add_observer(std::unique_ptr<property_observer> observer) {
        if (!observer) {
            throw std::invalid_argument("Cannot add null observer");
        }
        
        auto& ref = *observer;
        observers_.emplace_back(std::move(observer));
        
        // Sort observers by priority (highest first)
        std::ranges::sort(observers_, [](const auto& a, const auto& b) {
            return static_cast<std::uint8_t>(a->priority()) > static_cast<std::uint8_t>(b->priority());
        });
        
        return ref;
    }
    
    /**
     * @brief Add an observer with change handler
     * @param handler Change handler function
     * @param priority Priority level
     * @return Reference to the added observer
     */
    property_observer& add_observer(property_observer::change_handler handler,
                                  observer_priority priority = observer_priority::normal) {
        return add_observer(std::make_unique<property_observer>(std::move(handler), priority));
    }
    
    /**
     * @brief Remove an observer by its unique ID
     * @param observer_id Unique ID of observer to remove
     * @return true if observer was found and removed
     */
    bool remove_observer(const void* observer_id) {
        auto it = std::ranges::find_if(observers_, [observer_id](const auto& observer) {
            return observer->unique_id() == observer_id;
        });
        
        if (it != observers_.end()) {
            observers_.erase(it);
            return true;
        }
        return false;
    }
    
    /**
     * @brief Remove all observers
     */
    void clear() noexcept {
        observers_.clear();
    }
    
    /**
     * @brief Notify all observers
     */
    void notify_all() const {
        for (const auto& observer : observers_) {
            observer->notify();
        }
    }
    
    /**
     * @brief Get number of observers
     * @return Observer count
     */
    [[nodiscard]] size_type size() const noexcept {
        return static_cast<size_type>(observers_.size());
    }
    
    /**
     * @brief Check if there are any observers
     * @return true if empty
     */
    [[nodiscard]] bool empty() const noexcept {
        return observers_.empty();
    }
    
    /**
     * @brief Get enabled observer count
     * @return Number of enabled observers
     */
    [[nodiscard]] size_type enabled_count() const noexcept {
        return static_cast<size_type>(std::ranges::count_if(observers_, 
            [](const auto& observer) { return observer->is_enabled(); }));
    }

private:
    observer_container observers_;
};

/**
 * @brief Notification manager handles batched and delayed notifications
 */
class notification_manager {
public:
    /// @brief Container for pending notifications
    using notification_queue = std::vector<std::pair<const untyped_property_data*, property_observer_list*>>;

    /**
     * @brief Constructor
     */
    notification_manager() = default;
    
    /// @brief Destructor
    ~notification_manager() = default;
    
    MODERN_PROPERTY_DISABLE_COPY_MOVE(notification_manager)

    /**
     * @brief Begin a notification batch
     * 
     * During a batch, notifications are queued instead of sent immediately.
     * This allows for efficient handling of multiple property changes.
     */
    void begin_batch() noexcept {
        ++batch_depth_;
    }
    
    /**
     * @brief End a notification batch and send all queued notifications
     */
    void end_batch() {
        if (batch_depth_ > 0) {
            --batch_depth_;
            
            if (batch_depth_ == 0) {
                process_pending_notifications();
            }
        }
    }
    
    /**
     * @brief Check if currently in a batch
     * @return true if batching notifications
     */
    [[nodiscard]] bool is_batching() const noexcept {
        return batch_depth_ > 0;
    }
    
    /**
     * @brief Queue or immediately send a notification
     * @param property Property that changed
     * @param observers Observer list to notify
     */
    void notify(const untyped_property_data* property, property_observer_list* observers) {
        if (!property || !observers || observers->empty()) {
            return;
        }
        
        if (is_batching()) {
            // Check if already queued to avoid duplicates
            auto it = std::ranges::find_if(pending_notifications_,
                [property](const auto& pair) { return pair.first == property; });
            
            if (it == pending_notifications_.end()) {
                pending_notifications_.emplace_back(property, observers);
            }
        } else {
            // Send immediately
            observers->notify_all();
        }
    }

private:
    /**
     * @brief Process all pending notifications
     */
    void process_pending_notifications() {
        // Sort notifications by property address for consistent ordering
        std::ranges::sort(pending_notifications_, 
            [](const auto& a, const auto& b) {
                return a.first < b.first;
            });
        
        // Send all notifications
        for (const auto& [property, observers] : pending_notifications_) {
            observers->notify_all();
        }
        
        // Clear queue
        pending_notifications_.clear();
    }

    std::uint32_t batch_depth_{0};
    notification_queue pending_notifications_;
};

/**
 * @brief RAII helper for notification batching
 */
class notification_batch_guard {
public:
    /**
     * @brief Constructor - begins a notification batch
     * @param manager Notification manager to use
     */
    explicit notification_batch_guard(notification_manager& manager) noexcept
        : manager_{manager} {
        manager_.begin_batch();
    }
    
    /// @brief Destructor - ends the notification batch
    ~notification_batch_guard() noexcept {
        manager_.end_batch();
    }
    
    MODERN_PROPERTY_DISABLE_COPY_MOVE(notification_batch_guard)

private:
    notification_manager& manager_;
};

/**
 * @brief Factory functions for creating observers
 */
namespace observer_factory {
    /**
     * @brief Create a simple change observer
     * @param handler Function to call on changes
     * @param priority Observer priority
     * @return Unique pointer to observer
     */
    inline std::unique_ptr<property_observer> 
    create_change_observer(property_observer::change_handler handler,
                          observer_priority priority = observer_priority::normal) {
        return std::make_unique<property_observer>(std::move(handler), priority);
    }
    
    /**
     * @brief Create a typed value observer
     * @tparam T Property value type
     * @param handler Function to call with old/new values
     * @param priority Observer priority
     * @return Unique pointer to typed observer
     */
    template<Property_storable T>
    std::unique_ptr<typed_property_observer<T>>
    create_value_observer(typename typed_property_observer<T>::typed_change_handler handler,
                         observer_priority priority = observer_priority::normal) {
        return std::make_unique<typed_property_observer<T>>(std::move(handler), priority);
    }
}

} // namespace modern_property 