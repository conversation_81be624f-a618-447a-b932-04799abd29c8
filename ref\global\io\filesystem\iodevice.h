﻿#ifndef IODEVICE_H
#define IODEVICE_H

#include <cstdint>
#include <string>
#include <vector>
#include <memory>

namespace filesystem {

/**
 * @brief The IODevice class is the base class for all I/O devices.
 * 
 * IODevice provides a common interface for reading from and writing to
 * various devices like files, memory buffers, etc.
 */
class IODevice {
public:
    /**
     * @brief Defines the open mode for the device.
     */
    enum class OpenMode {
        NotOpen = 0x0000,
        ReadOnly = 0x0001,
        WriteOnly = 0x0002,
        ReadWrite = 0x0003, // ReadOnly | WriteOnly
        Append = 0x0004,
        Truncate = 0x0008,
        Text = 0x0010,
        Binary = 0x0020
    };

    /**
     * @brief Defines the position from which to seek.
     */
    enum class SeekOrigin {
        Begin,
        Current,
        End
    };

    /**
     * @brief Constructs a new IODevice.
     */
    IODevice();

    /**
     * @brief Virtual destructor.
     */
    virtual ~IODevice();

    /**
     * @brief Opens the device with the specified mode.
     * @param mode The open mode.
     * @return True if the device was opened successfully, false otherwise.
     */
    virtual bool open(OpenMode mode) = 0;

    /**
     * @brief Closes the device.
     */
    virtual void close() = 0;

    /**
     * @brief Returns whether the device is open.
     * @return True if the device is open, false otherwise.
     */
    virtual bool isOpen() const = 0;

    /**
     * @brief Returns the open mode of the device.
     * @return The open mode.
     */
    virtual OpenMode openMode() const;

    /**
     * @brief Returns the size of the device.
     * @return The size in bytes, or -1 if the size is unknown.
     */
    virtual int64_t size() const = 0;

    /**
     * @brief Returns the current position in the device.
     * @return The current position, or -1 if the position is unknown.
     */
    virtual int64_t pos() const = 0;

    /**
     * @brief Sets the current position in the device.
     * @param pos The new position.
     * @param origin The origin from which to seek.
     * @return The new position, or -1 if an error occurred.
     */
    virtual int64_t seek(int64_t pos, SeekOrigin origin = SeekOrigin::Begin) = 0;

    /**
     * @brief Returns whether the device is at the end.
     * @return True if the device is at the end, false otherwise.
     */
    virtual bool atEnd() const = 0;

    /**
     * @brief Reads data from the device.
     * @param data The buffer to read into.
     * @param maxSize The maximum number of bytes to read.
     * @return The number of bytes read, or -1 if an error occurred.
     */
    virtual int64_t read(char* data, int64_t maxSize) = 0;

    /**
     * @brief Reads all data from the device.
     * @return The data read, or an empty vector if an error occurred.
     */
    virtual std::vector<char> readAll();

    /**
     * @brief Reads a line from the device.
     * @param maxSize The maximum number of bytes to read.
     * @return The line read, or an empty string if an error occurred.
     */
    virtual std::string readLine(int64_t maxSize = 0);

    /**
     * @brief Writes data to the device.
     * @param data The data to write.
     * @param size The number of bytes to write.
     * @return The number of bytes written, or -1 if an error occurred.
     */
    virtual int64_t write(const char* data, int64_t size) = 0;

    /**
     * @brief Writes a string to the device.
     * @param data The string to write.
     * @return The number of bytes written, or -1 if an error occurred.
     */
    virtual int64_t write(const std::string& data);

    /**
     * @brief Flushes any buffered data to the device.
     * @return True if the data was flushed successfully, false otherwise.
     */
    virtual bool flush() = 0;

    /**
     * @brief Returns the error string for the last error.
     * @return The error string, or an empty string if no error occurred.
     */
    virtual std::string errorString() const = 0;

protected:
    /**
     * @brief Sets the open mode of the device.
     * @param mode The open mode.
     */
    void setOpenMode(OpenMode mode);

private:
    OpenMode m_openMode;
};

// Operator overloads for OpenMode
inline IODevice::OpenMode operator|(IODevice::OpenMode a, IODevice::OpenMode b) {
    return static_cast<IODevice::OpenMode>(static_cast<int>(a) | static_cast<int>(b));
}

inline IODevice::OpenMode operator&(IODevice::OpenMode a, IODevice::OpenMode b) {
    return static_cast<IODevice::OpenMode>(static_cast<int>(a) & static_cast<int>(b));
}

} // namespace filesystem

#endif // IODEVICE_H
