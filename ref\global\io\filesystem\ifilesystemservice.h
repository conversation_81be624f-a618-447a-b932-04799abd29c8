﻿#ifndef IFILESYSTEMSERVICE_H
#define IFILESYSTEMSERVICE_H

#include "fileinfogatherer.h"
// #include "filesystemmanager.h"

namespace filesystem {

class IFileSystemService : public QObject
{
    Q_OBJECT

public:
    IFileSystemService()
        : QObject{nullptr}
    {
        connectFileSystem();
    }

private:
    bool connectFileSystem()
    {
        // if (m_connected || !m_connectable) {
        //     return false;
        // }
        // // 如果从构造函数调用，这里传递的是基类指针, 而不是子类的指针, 无法在DatabaseManager中调用子类覆盖的虚函数
        // if (!database::DatabaseManager::instance().addService(this)) {
        //     qDebug() << "IDatabaseService Faild to addService.";
        //     return false;
        // }
        // m_connected = true;
        return true;
    }

    bool disconnectDatabase()
    {
        // if (m_connected && m_connectable) {
        //     database::DatabaseManager::instance().removeService(this);
        //     m_connected = false;
        //     return true;
        // }
        return false;
    }

    FileInfoGatherer* m_gatherer { nullptr };

    bool m_connectable { true };
    bool m_connected { false };
};

}
#endif // IFILESYSTEMSERVICE_H
