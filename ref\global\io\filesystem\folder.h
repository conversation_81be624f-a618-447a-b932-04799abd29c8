﻿#ifndef FOLDER_H
#define FOLDER_H

#include "path.h"
#include "fileinfo.h"
#include <string>
#include <vector>
#include <functional>

namespace filesystem {

/**
 * @brief The Folder class provides an interface for working with directories.
 * 
 * Folder provides methods for creating, removing, and navigating directories,
 * as well as listing their contents.
 */
class Folder {
public:
    /**
     * @brief Constructs a Folder with no path.
     */
    Folder();

    /**
     * @brief Constructs a Folder with the given path.
     * @param path The path to the directory.
     */
    explicit Folder(const std::string& path);

    /**
     * @brief Constructs a Folder with the given path.
     * @param path The path to the directory.
     */
    explicit Folder(const Path& path);

    /**
     * @brief Copy constructor.
     */
    Folder(const Folder& other);

    /**
     * @brief Move constructor.
     */
    Folder(Folder&& other) noexcept;

    /**
     * @brief Assignment operator.
     */
    Folder& operator=(const Folder& other);

    /**
     * @brief Move assignment operator.
     */
    Folder& operator=(Folder&& other) noexcept;

    /**
     * @brief Destructor.
     */
    ~Folder() = default;

    /**
     * @brief Returns the path of the directory.
     * @return The path.
     */
    std::string path() const;

    /**
     * @brief Sets the path of the directory.
     * @param path The new path.
     */
    void setPath(const std::string& path);

    /**
     * @brief Sets the path of the directory.
     * @param path The new path.
     */
    void setPath(const Path& path);

    /**
     * @brief Returns whether the directory exists.
     * @return True if the directory exists, false otherwise.
     */
    bool exists() const;

    /**
     * @brief Creates the directory.
     * @param createParents If true, creates parent directories as needed.
     * @return True if the directory was created successfully, false otherwise.
     */
    bool create(bool createParents = false);

    /**
     * @brief Removes the directory.
     * @param recursive If true, removes all contents recursively.
     * @return True if the directory was removed successfully, false otherwise.
     */
    bool remove(bool recursive = false);

    /**
     * @brief Renames the directory.
     * @param newName The new name for the directory.
     * @return True if the directory was renamed successfully, false otherwise.
     */
    bool rename(const std::string& newName);

    /**
     * @brief Returns the parent directory.
     * @return The parent directory.
     */
    Folder parentFolder() const;

    /**
     * @brief Returns the absolute path of the directory.
     * @return The absolute path.
     */
    std::string absolutePath() const;

    /**
     * @brief Returns the canonical path of the directory.
     * @return The canonical path.
     */
    std::string canonicalPath() const;

    /**
     * @brief Returns whether the directory is empty.
     * @return True if the directory is empty, false otherwise.
     */
    bool isEmpty() const;

    /**
     * @brief Returns the list of entries in the directory.
     * @param nameFilters List of name filters (e.g., "*.txt").
     * @param includeFiles Whether to include files in the result.
     * @param includeDirs Whether to include directories in the result.
     * @param includeHidden Whether to include hidden entries in the result.
     * @return The list of entries.
     */
    std::vector<FileInfo> entryList(
        const std::vector<std::string>& nameFilters = {},
        bool includeFiles = true,
        bool includeDirs = true,
        bool includeHidden = false) const;

    /**
     * @brief Returns the list of entry names in the directory.
     * @param nameFilters List of name filters (e.g., "*.txt").
     * @param includeFiles Whether to include files in the result.
     * @param includeDirs Whether to include directories in the result.
     * @param includeHidden Whether to include hidden entries in the result.
     * @return The list of entry names.
     */
    std::vector<std::string> entryNameList(
        const std::vector<std::string>& nameFilters = {},
        bool includeFiles = true,
        bool includeDirs = true,
        bool includeHidden = false) const;

    /**
     * @brief Returns the current working directory.
     * @return The current working directory.
     */
    static Folder current();

    /**
     * @brief Sets the current working directory.
     * @param path The new current working directory.
     * @return True if the current working directory was set successfully, false otherwise.
     */
    static bool setCurrent(const std::string& path);

    /**
     * @brief Returns the home directory.
     * @return The home directory.
     */
    static Folder home();

    /**
     * @brief Returns the temporary directory.
     * @return The temporary directory.
     */
    static Folder temp();

    /**
     * @brief Returns the root directory.
     * @return The root directory.
     */
    static Folder root();

private:
    std::string m_path;
    mutable std::string m_errorString;

    bool matchesFilter(const std::string& name, const std::vector<std::string>& filters) const;
};

} // namespace filesystem

#endif // FOLDER_H
