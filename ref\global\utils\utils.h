#pragma once
#ifndef GLOBAL_UTILS_H
#define GLOBAL_UTILS_H

#include <string>

#include "uuid.h"

inline std::string incrementNumberedName(const std::string& input)
{
    std::string extractedDigits;
    for (int i = input.length() - 1; i >= 0; --i) {
        if (std::isdigit(input[i])) {
            extractedDigits = input[i] + extractedDigits;
        } else {
            break;
        }
    }

    std::string result = input;
    if (extractedDigits.empty()) {
        result += " 0";
    } else {
        int number = std::stoi(extractedDigits);
        number++;
        result = input.substr(0, input.size() - extractedDigits.size()) + std::to_string(number);
    }

    return result;
}

inline std::string createUuid() {
    return Uuid::createUuid();
}

#endif // GLOBAL_UTILS_H
