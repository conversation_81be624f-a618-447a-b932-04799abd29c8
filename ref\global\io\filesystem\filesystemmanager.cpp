﻿#include "filesystemmanager.h"

using namespace filesystem;

/*FileSystemManager::FileSystemManager()
{
    m_fileInfoGatherer = new FileInfoGatherer;
    m_fileInfoGatherer->moveToThread(&m_fileInfoGathererThread);
    connect(&m_fileInfoGathererThread, &QThread::finished, m_fileInfoGatherer, &QObject::deleteLater);
    connect(this, &FileSystemManager::load, m_fileInfoGatherer, &FileInfoGatherer::getFileInfos);
    connect(m_fileInfoGatherer, &FileInfoGatherer::updates, this, [this](const std::vector<QFileInfo>& arg) {
        emit updates(arg);
    });
    connect(m_fileInfoGatherer, &FileInfoGatherer::directoryLoaded, this, [this](const QString& path) {
        emit loaded(path);
    });
}

FileSystemManager::~FileSystemManager()
{
    m_fileInfoGathererThread.quit();
    m_fileInfoGathererThread.wait();
}*/

/*Folder* FileSystemManager::getBlock(const QString& path)
{
    Folder* block = m_blocks[path];
    if (block) {
        return block;
    }
    block = new Folder(path);
    m_blocks.insert(path, block);
    return block;
}*/

FileSystemManager::FileSystemManager()
{
    m_fileInfoGatherer = std::make_unique<FileInfoGatherer>();

    // Set up callbacks
    m_fileInfoGatherer->setUpdatesCallback(
        [this](const std::string& path, const std::vector<FileInfo>& updates) {
            if (m_updatesCallback) {
                m_updatesCallback(path, updates);
            }
        });

    m_fileInfoGatherer->setDirectoryLoadedCallback(
        [this](const std::string& path) {
            if (m_directoryLoadedCallback) {
                m_directoryLoadedCallback(path);
            }
        });
}

void FileSystemManager::setUpdatesCallback(UpdatesCallback callback)
{
    m_updatesCallback = std::move(callback);
}

void FileSystemManager::setDirectoryLoadedCallback(DirectoryLoadedCallback callback)
{
    m_directoryLoadedCallback = std::move(callback);
}

/*QVariantList FileSystemManager::getDriver()
{
    QVariantList drivers;
    foreach (const QStorageInfo& storage, QStorageInfo::mountedVolumes()) {
        if (storage.isValid() && storage.isReady()) {
            QVariantMap props;
            QString path = storage.rootPath();
            QString display = path.mid(0, path.size() - 2);
            if (storage.device().startsWith("\\\\?\\Volume")) {
                display += " (" + storage.name() + ")";
            } else {
                QList<QByteArray> paths = storage.device().split('\\');
                display += " (" + paths.at(paths.size() - 2) + ")";
                display += " (" + paths.sliced(0, paths.size() - 2).join("\\") + ")";
            }

            props.insert("name", path.mid(0, path.size() - 1));
            props.insert("source", path);
            props.insert("display", display);
            props.insert("size", storage.bytesTotal() - storage.bytesFree());
            props.insert("total_size", storage.bytesTotal());

            drivers << props;
        }
    }
    return drivers;
}*/

void FileSystemManager::getFileInfos(const std::string& path)
{
    m_fileInfoGatherer->getFileInfos(path);
}
