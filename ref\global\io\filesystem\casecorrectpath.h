﻿#ifndef CASECORRECTPATH_H
#define CASECORRECTPATH_H

#include <Windows.h>
#include <string>
#include <atlbase.h>
#include <lm.h>

#include <QUrl>
#include <QFileInfo>
#include <QProcessEnvironment>
#include <QStorageInfo>

#include "settings.h"

const QStringList ROOT_PATHS { "this pc", "computer", "my computer" };
// 缺少实现的： home
const QMap<QString, QString> SYSTEM_PATHS {
    { "desktop", QStandardPaths::writableLocation(QStandardPaths::DesktopLocation) },
    { "/desktop", QStandardPaths::writableLocation(QStandardPaths::DesktopLocation) },
    { "documents", QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation) },
    { "downloads", QStandardPaths::writableLocation(QStandardPaths::DownloadLocation) },
    { "music", QStandardPaths::writableLocation(QStandardPaths::MusicLocation) },
    { "pictures", QStandardPaths::writableLocation(QStandardPaths::PicturesLocation) }
};

QString pathValidator(QString path, QString currentPath)
{
    path.replace('\\', '/');

    QString root = path.split('/').at(0).toLower();
    QString dir = path.sliced(root.size());
    if (root.isEmpty()) {
        if (path.startsWith("//")) {  // UNC
            return path;
        }
        return QStorageInfo::root().rootPath() + dir;  // system volume
    }

    if (root.size() == 2 && root.at(1) == ':') { // absolute path
        return path;
    }

    if (root == '.') {  // current
        return currentPath + path.sliced(1);
    }

    if (root == "..") {  // parent
        QDir currentDir(currentPath);
        currentDir.cdUp();
        return currentDir.absolutePath() + path.sliced(2);
    }

#if defined(Q_OS_WIN32)
    QVariantMap win32_shell_paths = Settings::instance()->value("win32_shell_paths").toMap();
    //if (path.toLower() == "cmd") {}
    if (root.size() > 6 && root.startsWith("shell:")
        && win32_shell_paths.contains(root)) {
        root = win32_shell_paths[root].toString();
        QString envPath = root.split('%').at(1);
        QProcessEnvironment env = QProcessEnvironment::systemEnvironment();
        root = env.value(envPath) + root.sliced(envPath.size() + 2);
        return root.replace('\\', '/') + dir;
    }
#endif
    if (ROOT_PATHS.contains(root)) {  // root
        if (path.contains('/')) {
            return dir;
        }
        return "";  // this pc
    }

    if (SYSTEM_PATHS.contains(root)) {  // system directories
        return SYSTEM_PATHS[root] + dir;
    }

    if (root.at(0) == '%' && root.right(1) == '%') {  // system env variable
        QProcessEnvironment env = QProcessEnvironment::systemEnvironment();
        QString value = env.value(root.sliced(1, root.size() - 2));
        if (!value.isEmpty()) {
            return value.replace('\\', '/') + '/' + dir;
        }
    }

    // 可以放在shell:之前，但这在非pathEditor输入的路径会造成奇怪的结果
    // 如点击Favorites的相对路径，会到转到当前文件夹内的结果
    if (QFileInfo::exists(currentPath + '/' + path)) { // relative path
        return currentPath + '/' + path;
    }

    qWarning() << "PathSync: The path is not matched.";
    return path;  // no match
}

QString pathCaseValidator(QString path)
{
    path = path.replace('\\', '/');
    if (path.startsWith("//")) {  // UNC
        path = "\\\\" + path.sliced(2);
    }
    QStringList parts = path.split('/');

    //qDebug() << path << parts;
    if (parts.isEmpty()) {
        return path;
    }
    QString current = parts.takeAt(0);
    QString result = current;
    if (!current.startsWith("\\\\")) {
        result = current.toUpper();
    } else { // UNC
        QUrl url(path);
        result = url.host();
    }
    foreach (QString part, parts) {
        //qDebug() << current;
        QDir currentDir(current);
        currentDir.setNameFilters({ part });
        QStringList dirs(currentDir.entryList());
        //qDebug() << current << part << dirs;
        if (!dirs.isEmpty()) {
            result += "/" + dirs.at(0);
        }
        current += "/" + part;
    }
    return result.replace('\\', '/');
}

static HRESULT correctPathCasing(std::wstring const &srcPath,
                                   std::wstring &dstPath)
{
    // 来源： https://stackoverflow.com/a/47353320/22058285
    // 缺点： UNC路径中的server name和share name部分不符合实际
    HRESULT hr = 0;
    CComPtr<IDispatch> disp;
    hr = disp.CoCreateInstance(L"Scripting.FileSystemObject");
    if (FAILED(hr)) return hr;

    CComVariant src(srcPath.c_str()), dst;
    hr = disp.Invoke1(L"GetAbsolutePathName", &src, &dst);
    if (FAILED(hr)) return hr;

    SIZE_T cch = SysStringLen(dst.bstrVal);
    dstPath = std::wstring(dst.bstrVal, cch);
    return hr;
}

static QString getCaseCorrectPath(QString path)
{
    // 来源： https://stackoverflow.com/a/54393259
    // 缺点： 不支持UNC

    if (path.isEmpty()) {
        return path;
    }

    // get individual parts of the path
    QStringList parts = QFileInfo(path).canonicalFilePath().split('/');
    if (parts.isEmpty()) {
        return path;
    }
    if (parts.size() == 1 || (parts.size() == 2 && parts.at(1).isEmpty())) {
        if (path.size() == 3 && path.at(1) == ':' && path.at(2) == '/') {
            return path.toUpper();
        } else {
            qDebug() << "!!! UNC 路径未做大小写矫正 !!!";
            return path;
        }
    }

    // we start with the drive path
    QString correctPath = parts.takeFirst().toUpper();

    // now we incrementally add parts one by one
    for (const QString& part : qAsConst(parts)) {
        QString tempPath = correctPath + '\\' + part;

        WIN32_FIND_DATA data = {};
        HANDLE sh = FindFirstFileW(LPCWSTR(tempPath.utf16()), &data);
        if (sh == INVALID_HANDLE_VALUE) {
            qDebug() << "PathSync: INVALID_HANDLE_VALUE";
            return path;
        }
        FindClose(sh);

        // add the correct name
        correctPath += '\\' + QString::fromWCharArray(data.cFileName);
    }

    return correctPath.replace('\\', '/');
}

static std::wstring getActualPathName( const wchar_t* path )
{
    // 来源： https://stackoverflow.com/a/81493/22058285
    // 缺点： 1.UNC路径中的server name和share name部分不符合实际
    // 2.不支持超长路径，3.对于特定的路径不返回实际值(User->用户)

    // This is quite involved, but the meat is SHGetFileInfo
    const wchar_t kSeparator = L'\\';

    // copy input string because we'll be temporary modifying it in place
    size_t length = wcslen(path);
    wchar_t buffer[MAX_PATH];
    memcpy( buffer, path, (length+1) * sizeof(path[0]) );

    size_t i = 0;

    std::wstring result;

    // for network paths (\\server\share\RestOfPath), getting the display
    // name mangles it into unusable form (e.g. "\\server\share" turns
    // into "share on server (server)"). So detect this case and just skip
    // up to two path components
    if( length >= 2 && buffer[0] == kSeparator && buffer[1] == kSeparator )
    {
        int skippedCount = 0;
        i = 2; // start after '\\'
        while( i < length && skippedCount < 2 )
        {
            if( buffer[i] == kSeparator )
                ++skippedCount;
            ++i;
        }

        result.append( buffer, i );
    }
    // for drive names, just add it uppercased
    else if( length >= 2 && buffer[1] == L':' )
    {
        result += towupper(buffer[0]);
        result += L':';
        if( length >= 3 && buffer[2] == kSeparator )
        {
            result += kSeparator;
            i = 3; // start after drive, colon and separator
        }
        else
        {
            i = 2; // start after drive and colon
        }
    }

    size_t lastComponentStart = i;
    bool addSeparator = false;

    while( i < length )
    {
        // skip until path separator
        while( i < length && buffer[i] != kSeparator )
            ++i;

        if( addSeparator )
            result += kSeparator;

        // if we found path separator, get real filename of this
        // last path name component
        bool foundSeparator = (i < length);
        buffer[i] = 0;
        SHFILEINFOW info;

        // nuke the path separator so that we get real name of current path component
        info.szDisplayName[0] = 0;
        if( SHGetFileInfoW( buffer, 0, &info, sizeof(info), SHGFI_DISPLAYNAME ) )
        {
            result += info.szDisplayName;
        }
        else
        {
            // most likely file does not exist.
            // So just append original path name component.
            result.append( buffer + lastComponentStart, i - lastComponentStart );
        }

        // restore path separator that we might have nuked before
        if( foundSeparator )
            buffer[i] = kSeparator;

        ++i;
        lastComponentStart = i;
        addSeparator = true;
    }

    return result;
}

static std::wstring getCorrectCasedPath(const std::wstring& path)
{
    // 来源: chatGPT 3.5
    // 缺点： UNC路径的返回值不符合实际
    std::wstring result;

    HANDLE fileHandle = CreateFileW(
        path.c_str(),
        FILE_READ_ATTRIBUTES,
        FILE_SHARE_READ | FILE_SHARE_WRITE,
        NULL,
        OPEN_EXISTING,
        FILE_FLAG_BACKUP_SEMANTICS,
        NULL);

    if (fileHandle != INVALID_HANDLE_VALUE) {
        wchar_t buffer[MAX_PATH];
        if (GetFinalPathNameByHandleW(fileHandle, buffer, MAX_PATH, 0)) {
            result = buffer;
        }
        CloseHandle(fileHandle);
    }
    return result;
}

#endif // CASECORRECTPATH_H
