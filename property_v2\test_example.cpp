/**
 * @file test_example.cpp
 * @brief Property System V2 Complete Test Example
 *
 * Demonstrates core functionality of the modern property system:
 * - Basic property creation and value operations
 * - Property binding setup and automatic updates
 * - Simple observer pattern usage
 *
 * Compile command (MSVC):
 * cl /std:c++20 /EHsc test_example.cpp
 *
 * <AUTHOR> System V2
 * @date 2025
 */

#include <iostream>
#include <string>
#include <cassert>
#include <functional>
#include <vector>
#include <type_traits>

// Simplified property system implementation focusing on core functionality
namespace property_v2 {

// Simplified type traits
namespace core {
    template<typename T>
    using parameter_type_t = std::conditional_t<
        std::is_arithmetic_v<T> || std::is_enum_v<T> || std::is_pointer_v<T>,
        T, const T&>;
}

// Simplified binding system
namespace binding {
    template<typename T>
    class property_binding {
    public:
        using function_type = std::function<T()>;
        
        property_binding() = default;
        
        template<typename F>
        property_binding(F&& func) : func_(std::forward<F>(func)) {}
        
        bool is_null() const { return !func_; }
        
        T evaluate() const {
            return func_ ? func_() : T{};
        }
        
    private:
        function_type func_;
    };
}

// Simplified observer system
namespace observer {
    class change_handler {
    public:
        using handler_fn = std::function<void()>;
        
        change_handler() = default;
        change_handler(handler_fn handler) : handler_(std::move(handler)) {}
        
        void notify() const {
            if (handler_) {
                handler_();
            }
        }
        
        bool is_valid() const { return static_cast<bool>(handler_); }
        
    private:
        handler_fn handler_;
    };
}

// Main property class
namespace property {
    template<typename T>
    class property {
    public:
        using value_type = T;
        using parameter_type = core::parameter_type_t<T>;
        using binding_type = binding::property_binding<T>;
        
        // Constructors
        property() = default;
        explicit property(parameter_type initial_value) : value_(initial_value) {}
        
        template<typename F>
        explicit property(F&& func) : binding_(std::forward<F>(func)) {
            update_from_binding();
        }
        
        // Value access
        parameter_type value() const {
            if (!binding_.is_null()) {
                // If there's a binding, get value from binding
                const_cast<property*>(this)->update_from_binding();
            }
            return value_;
        }
        
        void set_value(parameter_type new_value) {
            if (value_ != new_value) {
                binding_ = binding_type{}; // Clear binding
                value_ = new_value;
                notify_observers();
            }
        }
        
        // Operator overloads
        property& operator=(parameter_type new_value) {
            set_value(new_value);
            return *this;
        }
        
        operator parameter_type() const {
            return value();
        }
        
        // Binding management
        template<typename F>
        binding_type set_binding(F&& func) {
            auto old_binding = std::move(binding_);
            binding_ = binding_type(std::forward<F>(func));
            update_from_binding();
            return old_binding;
        }
        
        bool has_binding() const {
            return !binding_.is_null();
        }
        
        binding_type take_binding() {
            auto old_binding = std::move(binding_);
            binding_ = binding_type{};
            return old_binding;
        }
        
        // Observers
        template<typename F>
        observer::change_handler on_value_changed(F&& handler) {
            auto change_handler = observer::change_handler(std::forward<F>(handler));
            observers_.push_back(change_handler);
            return change_handler;
        }
        
        template<typename F>
        observer::change_handler subscribe(F&& handler) {
            handler(); // Call immediately once
            return on_value_changed(std::forward<F>(handler));
        }
        
    private:
        T value_{};
        binding_type binding_;
        std::vector<observer::change_handler> observers_;
        
        void update_from_binding() {
            if (!binding_.is_null()) {
                T new_value = binding_.evaluate();
                if (value_ != new_value) {
                    value_ = new_value;
                    notify_observers();
                }
            }
        }
        
        void notify_observers() {
            for (auto& observer : observers_) {
                observer.notify();
            }
        }
    };
}

} // namespace property_v2

// Use aliases to simplify code
using namespace property_v2;

/**
 * @brief Test basic property operations
 */
void test_basic_operations() {
    std::cout << "\n=== Test Basic Property Operations ===" << std::endl;

    // Create properties
    property::property<int> x{42};
    property::property<std::string> name{"Hello"};

    // Verify initial values
    assert(x.value() == 42);
    assert(name.value() == "Hello");
    std::cout << "✓ Initial values correct: x=" << x.value() << ", name=" << name.value() << std::endl;

    // Set new values
    x.set_value(100);
    name = "World";  // Use assignment operator

    assert(x.value() == 100);
    assert(name.value() == "World");
    std::cout << "✓ Value setting successful: x=" << x.value() << ", name=" << name.value() << std::endl;

    // Type conversion
    int auto_value = x;  // Automatic conversion
    assert(auto_value == 100);
    std::cout << "✓ Automatic type conversion successful: " << auto_value << std::endl;
}

/**
 * @brief Test property binding
 */
void test_property_binding() {
    std::cout << "\n=== Test Property Binding ===" << std::endl;
    
    property::property<int> x{10};
    property::property<int> y{20};

    // Create bound property
    property::property<int> sum;
    sum.set_binding([&x, &y]() {
        return x.value() + y.value();
    });

    assert(sum.value() == 30);
    std::cout << "✓ Initial binding correct: x=" << x.value() << ", y=" << y.value() << ", sum=" << sum.value() << std::endl;

    // Change x, sum should auto-update
    x = 15;
    assert(sum.value() == 35);
    std::cout << "✓ Binding updated after x change: x=" << x.value() << ", sum=" << sum.value() << std::endl;

    // Change y, sum should also auto-update
    y = 25;
    assert(sum.value() == 40);
    std::cout << "✓ Binding updated after y change: y=" << y.value() << ", sum=" << sum.value() << std::endl;

    // Create binding using constructor
    auto product = property::property<int>{[&x, &y]() {
        return x.value() * y.value();
    }};

    assert(product.value() == 375); // 15 * 25
    std::cout << "✓ Constructor binding correct: product=" << product.value() << std::endl;

    // Test binding removal
    assert(sum.has_binding());
    auto old_binding = sum.take_binding();
    assert(!sum.has_binding());
    sum = 999; // Set fixed value
    assert(sum.value() == 999);
    std::cout << "✓ Binding removal successful: sum=" << sum.value() << std::endl;
}

/**
 * @brief Test observer pattern
 */
void test_observers() {
    std::cout << "\n=== Test Observer Pattern ===" << std::endl;
    
    property::property<int> counter{0};
    int notification_count = 0;
    int last_observed_value = -1;
    
    // 创建变更处理器
    auto change_handler = counter.on_value_changed([&notification_count]() {
        notification_count++;
        std::cout << "  → 变更通知 #" << notification_count << std::endl;
    });
    
    // 创建值观察器
    auto value_observer = counter.on_value_changed([&counter, &last_observed_value]() {
        last_observed_value = counter.value();
        std::cout << "  → 值观察: counter=" << last_observed_value << std::endl;
    });
    
    // 订阅（立即调用一次）
    bool subscription_called = false;
    auto subscription = counter.subscribe([&subscription_called]() {
        subscription_called = true;
        std::cout << "  → 订阅回调执行" << std::endl;
    });
    
    assert(subscription_called);
    std::cout << "✓ 订阅立即回调成功" << std::endl;
    
    // 测试变更通知
    std::cout << "开始变更测试:" << std::endl;
    counter = 1;
    assert(notification_count == 1);
    assert(last_observed_value == 1);
    
    counter = 2;
    assert(notification_count == 2);
    assert(last_observed_value == 2);
    
    counter = 3;
    assert(notification_count == 3);
    assert(last_observed_value == 3);
    
    std::cout << "✓ 观察者通知正确: 总通知次数=" << notification_count << std::endl;
}

/**
 * @brief 测试复杂绑定场景
 */
void test_complex_binding() {
    std::cout << "\n=== 测试复杂绑定场景 ===" << std::endl;
    
    property::property<int> a{5};
    property::property<int> b{10};
    
    // 多级绑定
    property::property<int> sum;
    sum.set_binding([&a, &b]() { return a.value() + b.value(); });
    
    property::property<int> doubled_sum;
    doubled_sum.set_binding([&sum]() { return sum.value() * 2; });
    
    assert(sum.value() == 15);
    assert(doubled_sum.value() == 30);
    std::cout << "✓ 多级绑定初始值正确: sum=" << sum << ", doubled_sum=" << doubled_sum << std::endl;
    
    // 改变源值，验证级联更新
    a = 8;
    assert(sum.value() == 18);
    assert(doubled_sum.value() == 36);
    std::cout << "✓ 级联更新正确: a=" << a << ", sum=" << sum << ", doubled_sum=" << doubled_sum << std::endl;
    
    // 字符串绑定
    property::property<std::string> message;
    message.set_binding([&a, &b]() { 
        return "a=" + std::to_string(a.value()) + ", b=" + std::to_string(b.value()); 
    });
    
    std::string expected = "a=8, b=10";
    assert(message.value() == expected);
    std::cout << "✓ 字符串绑定正确: " << message.value() << std::endl;
    
    b = 15;
    expected = "a=8, b=15";
    assert(message.value() == expected);
    std::cout << "✓ 字符串绑定更新正确: " << message.value() << std::endl;
}

/**
 * @brief 主函数
 */
int main() {
    std::cout << "Property System V2 - 完整测试示例" << std::endl;
    std::cout << "编译器: MSVC C++20" << std::endl;
    std::cout << "========================================" << std::endl;
    
    try {
        test_basic_operations();
        test_property_binding();
        test_observers();
        test_complex_binding();
        
        std::cout << "\n========================================" << std::endl;
        std::cout << "🎉 所有测试通过！属性系统工作正常。" << std::endl;
        std::cout << "✓ 基本操作功能正确" << std::endl;
        std::cout << "✓ 属性绑定功能正确" << std::endl;
        std::cout << "✓ 观察者模式功能正确" << std::endl;
        std::cout << "✓ 复杂绑定场景功能正确" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "❌ 测试失败: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
