#pragma once

/**
 * @file performance.hpp
 * @brief 属性系统性能监控
 * 
 * 提供属性系统的性能监控和分析工具。
 * 
 * <AUTHOR> System V2
 * @date 2025
 */

#include <chrono>
#include <atomic>
#include <unordered_map>
#include <string>
#include <memory>
#include <mutex>
#include <vector>
#include "../core/utils.hpp"

namespace property_v2::support {

/**
 * @brief 性能计数器
 */
class performance_counter {
public:
    /**
     * @brief 增加计数
     */
    void increment() noexcept {
        count_.fetch_add(1, std::memory_order_relaxed);
    }
    
    /**
     * @brief 增加指定数量
     */
    void add(std::size_t value) noexcept {
        count_.fetch_add(value, std::memory_order_relaxed);
    }
    
    /**
     * @brief 获取当前计数
     */
    std::size_t get() const noexcept {
        return count_.load(std::memory_order_relaxed);
    }
    
    /**
     * @brief 重置计数
     */
    void reset() noexcept {
        count_.store(0, std::memory_order_relaxed);
    }

private:
    std::atomic<std::size_t> count_{0};
};

/**
 * @brief 性能计时器
 */
class performance_timer {
public:
    using clock_type = std::chrono::high_resolution_clock;
    using duration_type = std::chrono::nanoseconds;
    
    /**
     * @brief 开始计时
     */
    void start() noexcept {
        start_time_ = clock_type::now();
    }
    
    /**
     * @brief 停止计时并累加时间
     */
    void stop() noexcept {
        auto end_time = clock_type::now();
        auto duration = std::chrono::duration_cast<duration_type>(end_time - start_time_);
        total_time_.fetch_add(duration.count(), std::memory_order_relaxed);
        count_.increment();
    }
    
    /**
     * @brief 获取总时间（纳秒）
     */
    std::size_t total_nanoseconds() const noexcept {
        return total_time_.load(std::memory_order_relaxed);
    }
    
    /**
     * @brief 获取总时间（毫秒）
     */
    double total_milliseconds() const noexcept {
        return total_nanoseconds() / 1000000.0;
    }
    
    /**
     * @brief 获取平均时间（纳秒）
     */
    double average_nanoseconds() const noexcept {
        auto count = count_.get();
        return count > 0 ? static_cast<double>(total_nanoseconds()) / count : 0.0;
    }
    
    /**
     * @brief 获取平均时间（毫秒）
     */
    double average_milliseconds() const noexcept {
        return average_nanoseconds() / 1000000.0;
    }
    
    /**
     * @brief 获取调用次数
     */
    std::size_t call_count() const noexcept {
        return count_.get();
    }
    
    /**
     * @brief 重置计时器
     */
    void reset() noexcept {
        total_time_.store(0, std::memory_order_relaxed);
        count_.reset();
    }

private:
    clock_type::time_point start_time_;
    std::atomic<std::size_t> total_time_{0};
    performance_counter count_;
};

/**
 * @brief 作用域计时器
 * 
 * RAII风格的计时器，在构造时开始计时，析构时停止计时。
 */
class scoped_timer {
public:
    /**
     * @brief 构造函数
     * @param timer 要使用的计时器
     */
    explicit scoped_timer(performance_timer& timer) noexcept : timer_(timer) {
        timer_.start();
    }
    
    /**
     * @brief 析构函数
     */
    ~scoped_timer() noexcept {
        timer_.stop();
    }
    
    PROPERTY_DISABLE_COPY_MOVE(scoped_timer)

private:
    performance_timer& timer_;
};

/**
 * @brief 性能统计信息
 */
struct performance_stats {
    std::size_t binding_evaluations = 0;       ///< 绑定求值次数
    std::size_t property_reads = 0;            ///< 属性读取次数
    std::size_t property_writes = 0;           ///< 属性写入次数
    std::size_t observer_notifications = 0;    ///< 观察者通知次数
    std::size_t binding_loops_detected = 0;    ///< 检测到的绑定循环次数
    
    double total_binding_time_ms = 0.0;        ///< 总绑定时间（毫秒）
    double average_binding_time_ms = 0.0;      ///< 平均绑定时间（毫秒）
    double total_notification_time_ms = 0.0;   ///< 总通知时间（毫秒）
    double average_notification_time_ms = 0.0; ///< 平均通知时间（毫秒）
    
    /**
     * @brief 重置统计信息
     */
    void reset() noexcept {
        *this = performance_stats{};
    }
    
    /**
     * @brief 转换为字符串
     */
    std::string to_string() const {
        std::string result;
        result += "Binding evaluations: " + std::to_string(binding_evaluations) + "\n";
        result += "Property reads: " + std::to_string(property_reads) + "\n";
        result += "Property writes: " + std::to_string(property_writes) + "\n";
        result += "Observer notifications: " + std::to_string(observer_notifications) + "\n";
        result += "Binding loops detected: " + std::to_string(binding_loops_detected) + "\n";
        result += "Total binding time: " + std::to_string(total_binding_time_ms) + " ms\n";
        result += "Average binding time: " + std::to_string(average_binding_time_ms) + " ms\n";
        result += "Total notification time: " + std::to_string(total_notification_time_ms) + " ms\n";
        result += "Average notification time: " + std::to_string(average_notification_time_ms) + " ms\n";
        return result;
    }
};

/**
 * @brief 性能监控器
 * 
 * 全局性能监控和统计。
 */
class performance_monitor {
public:
    /**
     * @brief 获取全局实例
     */
    static performance_monitor& instance() {
        static performance_monitor instance_;
        return instance_;
    }
    
    /**
     * @brief 启用性能监控
     */
    void enable() noexcept {
        enabled_.store(true, std::memory_order_relaxed);
    }
    
    /**
     * @brief 禁用性能监控
     */
    void disable() noexcept {
        enabled_.store(false, std::memory_order_relaxed);
    }
    
    /**
     * @brief 检查是否启用
     */
    bool is_enabled() const noexcept {
        return enabled_.load(std::memory_order_relaxed);
    }
    
    /**
     * @brief 记录绑定求值
     */
    void record_binding_evaluation() noexcept {
        if (is_enabled()) {
            binding_evaluations_.increment();
        }
    }
    
    /**
     * @brief 记录属性读取
     */
    void record_property_read() noexcept {
        if (is_enabled()) {
            property_reads_.increment();
        }
    }
    
    /**
     * @brief 记录属性写入
     */
    void record_property_write() noexcept {
        if (is_enabled()) {
            property_writes_.increment();
        }
    }
    
    /**
     * @brief 记录观察者通知
     */
    void record_observer_notification() noexcept {
        if (is_enabled()) {
            observer_notifications_.increment();
        }
    }
    
    /**
     * @brief 记录绑定循环
     */
    void record_binding_loop() noexcept {
        if (is_enabled()) {
            binding_loops_.increment();
        }
    }
    
    /**
     * @brief 获取绑定计时器
     */
    performance_timer& binding_timer() noexcept {
        return binding_timer_;
    }
    
    /**
     * @brief 获取通知计时器
     */
    performance_timer& notification_timer() noexcept {
        return notification_timer_;
    }
    
    /**
     * @brief 获取性能统计信息
     */
    performance_stats get_stats() const {
        performance_stats stats;
        stats.binding_evaluations = binding_evaluations_.get();
        stats.property_reads = property_reads_.get();
        stats.property_writes = property_writes_.get();
        stats.observer_notifications = observer_notifications_.get();
        stats.binding_loops_detected = binding_loops_.get();
        
        stats.total_binding_time_ms = binding_timer_.total_milliseconds();
        stats.average_binding_time_ms = binding_timer_.average_milliseconds();
        stats.total_notification_time_ms = notification_timer_.total_milliseconds();
        stats.average_notification_time_ms = notification_timer_.average_milliseconds();
        
        return stats;
    }
    
    /**
     * @brief 重置所有统计信息
     */
    void reset() noexcept {
        binding_evaluations_.reset();
        property_reads_.reset();
        property_writes_.reset();
        observer_notifications_.reset();
        binding_loops_.reset();
        binding_timer_.reset();
        notification_timer_.reset();
    }

private:
    performance_monitor() = default;
    
    std::atomic<bool> enabled_{false};
    performance_counter binding_evaluations_;
    performance_counter property_reads_;
    performance_counter property_writes_;
    performance_counter observer_notifications_;
    performance_counter binding_loops_;
    performance_timer binding_timer_;
    performance_timer notification_timer_;
};

/**
 * @brief 性能监控便利宏
 */
#define PROPERTY_PERF_RECORD_BINDING_EVAL() \
    ::property_v2::support::performance_monitor::instance().record_binding_evaluation()

#define PROPERTY_PERF_RECORD_PROPERTY_READ() \
    ::property_v2::support::performance_monitor::instance().record_property_read()

#define PROPERTY_PERF_RECORD_PROPERTY_WRITE() \
    ::property_v2::support::performance_monitor::instance().record_property_write()

#define PROPERTY_PERF_RECORD_OBSERVER_NOTIFICATION() \
    ::property_v2::support::performance_monitor::instance().record_observer_notification()

#define PROPERTY_PERF_RECORD_BINDING_LOOP() \
    ::property_v2::support::performance_monitor::instance().record_binding_loop()

#define PROPERTY_PERF_TIME_BINDING() \
    ::property_v2::support::scoped_timer timer_##__LINE__(::property_v2::support::performance_monitor::instance().binding_timer())

#define PROPERTY_PERF_TIME_NOTIFICATION() \
    ::property_v2::support::scoped_timer timer_##__LINE__(::property_v2::support::performance_monitor::instance().notification_timer())

} // namespace property_v2::support
