#pragma once

#include "types.h"
#include <utility>
#include <optional>
#include <memory>

/**
 * @brief Modern Property System - Property Data Storage
 * 
 * This file contains the property data storage classes that manage
 * the actual values and their associated metadata.
 */

namespace modern_property {

/**
 * @brief Base class for untyped property data
 * 
 * This class provides the common interface for all property data,
 * regardless of the stored type. It serves as the foundation for
 * the type-erased property system.
 */
class untyped_property_data {
public:
    /// @brief Default constructor
    untyped_property_data() = default;
    
    /// @brief Virtual destructor for proper cleanup
    virtual ~untyped_property_data() = default;
    
    /// @brief Move constructor
    untyped_property_data(untyped_property_data&&) = default;
    
    /// @brief Move assignment operator
    untyped_property_data& operator=(untyped_property_data&&) = default;
    
    MODERN_PROPERTY_DISABLE_COPY(untyped_property_data)
    
    /**
     * @brief Get type information for debugging
     * @return Type information string
     */
    [[nodiscard]] virtual string_type type_name() const = 0;
    
    /**
     * @brief Check if this property has an active binding
     * @return true if bound, false otherwise
     */
    [[nodiscard]] virtual bool has_binding() const noexcept = 0;
    
    /**
     * @brief Get unique identifier for this property
     * @return Pointer-based unique identifier
     */
    [[nodiscard]] const void* unique_id() const noexcept { 
        return static_cast<const void*>(this); 
    }

protected:
    /**
     * @brief Register this property as a dependency in current evaluation context
     */
    void register_dependency() const;
    
    /**
     * @brief Notify observers that this property has changed
     */
    virtual void notify_observers() const = 0;
    
    /**
     * @brief Called when property value changes to trigger observer notifications
     */
    void value_changed() const {
        notify_observers();
    }
};

/**
 * @brief Typed property data storage with modern C++20 features
 * @tparam T The type of value stored in this property
 */
template<Property_storable T>
class property_data : public untyped_property_data {
public:
    /// @brief Value type stored by this property
    using value_type = T;
    
    /// @brief Parameter type for efficient passing
    using const_param_type = typename parameter_traits<T>::const_param_type;
    using param_type = typename parameter_traits<T>::param_type;
    using rvalue_type = typename parameter_traits<T>::rvalue_type;

    /**
     * @brief Default constructor - value-initializes the stored value
     */
    property_data() requires std::is_default_constructible_v<T>
        : value_{} {}
    
    /**
     * @brief Constructor with initial value (copy)
     * @param initial_value The initial value to store
     */
    explicit property_data(const_param_type initial_value) 
        requires std::is_copy_constructible_v<T>
        : value_{initial_value} {}
    
    /**
     * @brief Constructor with initial value (move)
     * @param initial_value The initial value to store (moved)
     */
    explicit property_data(rvalue_type initial_value) 
        requires std::is_move_constructible_v<T>
        : value_{std::move(initial_value)} {}
    
    /// @brief Default destructor
    ~property_data() override = default;
    
    /// @brief Move constructor
    property_data(property_data&&) = default;
    
    /// @brief Move assignment
    property_data& operator=(property_data&&) = default;
    
    MODERN_PROPERTY_DISABLE_COPY(property_data)

    /**
     * @brief Get the current value (const version)
     * @return Current value of the property
     */
    [[nodiscard]] const_param_type value() const noexcept(std::is_nothrow_copy_constructible_v<T>) {
        register_dependency();
        return value_;
    }
    
    /**
     * @brief Get direct access to value bypassing dependency tracking
     * @return Current value without registering dependency
     * @warning This method bypasses the binding system - use with caution
     */
    [[nodiscard]] const_param_type value_bypassing_bindings() const noexcept(std::is_nothrow_copy_constructible_v<T>) {
        return value_;
    }

    /**
     * @brief Set new value with change detection
     * @param new_value The new value to store
     * @return true if value actually changed, false if same
     */
    bool set_value(const_param_type new_value) 
        requires std::is_copy_assignable_v<T> {
        if constexpr (Equality_comparable<T>) {
            if (value_ == new_value) {
                return false; // No change
            }
        }
        value_ = new_value;
        value_changed();
        return true;
    }
    
    /**
     * @brief Set new value with change detection (move version)
     * @param new_value The new value to store (moved)
     * @return true if value actually changed, false if same
     */
    bool set_value(rvalue_type new_value) 
        requires std::is_move_assignable_v<T> {
        if constexpr (Equality_comparable<T>) {
            if (value_ == new_value) {
                return false; // No change
            }
        }
        value_ = std::move(new_value);
        value_changed();
        return true;
    }
    
    /**
     * @brief Set value bypassing change notifications and binding system
     * @param new_value The new value to store
     * @warning This method bypasses change detection and notifications
     */
    void set_value_bypassing_bindings(const_param_type new_value) 
        requires std::is_copy_assignable_v<T> {
        value_ = new_value;
    }
    
    /**
     * @brief Set value bypassing change notifications and binding system (move version)
     * @param new_value The new value to store (moved)
     * @warning This method bypasses change detection and notifications
     */
    void set_value_bypassing_bindings(rvalue_type new_value) 
        requires std::is_move_assignable_v<T> {
        value_ = std::move(new_value);
    }

    /**
     * @brief Get type name for debugging
     * @return String representation of the stored type
     */
    [[nodiscard]] string_type type_name() const override {
        return typeid(T).name();
    }
    
    /**
     * @brief Check if this property currently has a binding
     * @return false for base property_data (bindings managed by derived classes)
     */
    [[nodiscard]] bool has_binding() const noexcept override {
        return false; // Base property_data has no binding management
    }

    /**
     * @brief Direct access to the underlying value
     * @return Reference to the stored value
     * @warning Direct access bypasses all safety mechanisms
     */
    [[nodiscard]] const T& unsafe_value() const noexcept {
        return value_;
    }
    
    /**
     * @brief Direct mutable access to the underlying value
     * @return Mutable reference to the stored value
     * @warning Direct access bypasses all safety mechanisms
     */
    [[nodiscard]] T& unsafe_mutable_value() noexcept {
        return value_;
    }

protected:
    /**
     * @brief Notify observers - implemented by derived classes
     */
    void notify_observers() const override {
        // Base implementation does nothing
        // Derived classes (like property<T>) will implement this
    }
    
    /**
     * @brief Update value from binding evaluation
     * @param new_value New value computed by binding
     * @return true if value changed
     */
    bool update_from_binding(const_param_type new_value) 
        requires std::is_copy_assignable_v<T> {
        if constexpr (Equality_comparable<T>) {
            if (value_ == new_value) {
                return false;
            }
        }
        value_ = new_value;
        return true;
    }
    
    /**
     * @brief Update value from binding evaluation (move version)
     * @param new_value New value computed by binding (moved)
     * @return true if value changed
     */
    bool update_from_binding(rvalue_type new_value) 
        requires std::is_move_assignable_v<T> {
        if constexpr (Equality_comparable<T>) {
            if (value_ == new_value) {
                return false;
            }
        }
        value_ = std::move(new_value);
        return true;
    }

private:
    /// @brief The actual stored value
    T value_;
};

/**
 * @brief Type trait to check if a type is a property_data
 * @tparam T Type to check
 */
template<typename T>
struct is_property_data : std::false_type {};

template<Property_storable T>
struct is_property_data<property_data<T>> : std::true_type {};

/// @brief Convenience variable template for is_property_data
template<typename T>
constexpr bool is_property_data_v = is_property_data<T>::value;

/**
 * @brief Utility functions for property data manipulation
 */
namespace property_data_utils {
    /**
     * @brief Safely cast untyped property data to typed version
     * @tparam T Expected property type
     * @param data Untyped property data pointer
     * @return Typed property data pointer or nullptr if cast fails
     */
    template<Property_storable T>
    [[nodiscard]] property_data<T>* safe_cast(untyped_property_data* data) noexcept {
        return dynamic_cast<property_data<T>*>(data);
    }
    
    /**
     * @brief Safely cast untyped property data to typed version (const)
     * @tparam T Expected property type  
     * @param data Untyped property data pointer
     * @return Typed property data pointer or nullptr if cast fails
     */
    template<Property_storable T>
    [[nodiscard]] const property_data<T>* safe_cast(const untyped_property_data* data) noexcept {
        return dynamic_cast<const property_data<T>*>(data);
    }
}

} // namespace modern_property 